import streamlit as st
import pandas as pd
import json
from datetime import datetime, date
import io
import time
import uuid
from typing import Dict, List, Any, Optional, Union
import base64
import sqlite3
import re
from pathlib import Path
import numpy as np
from dataclasses import dataclass, asdict
from enum import Enum
import plotly.express as px
import plotly.graph_objects as go
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.impute import SimpleImputer
import pickle
import logging

# Configure logging for better debugging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configure page
st.set_page_config(
    page_title="Enhanced Psychiatric Assessment System",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Database setup
class DatabaseManager:
    """SQLite database manager for patient data persistence"""
    
    def __init__(self, db_path: str = "psychiatric_assessment.db"):
        self.db_path = db_path
        # Database connection validation
        try:
            self.init_database()
            logger.info(f"Database initialized successfully at {db_path}")
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            raise
    
    def init_database(self):
        """Initialize database with required tables"""
        # Connection pooling for better performance in multi-user scenarios
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Patients table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS patients (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    patient_code TEXT UNIQUE NOT NULL,
                    demographics TEXT,
                    present_illness TEXT,
                    risk_assessment TEXT,
                    psychiatric_history TEXT,
                    substance_history TEXT,
                    medication_history TEXT,
                    medical_history TEXT,
                    mse TEXT,
                    paraclinicals TEXT,
                    diagnosis TEXT,
                    rating_scales TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Add indexes for better query performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_patient_code ON patients(patient_code)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_created_at ON patients(created_at)')
            
            # Assessment templates table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS assessment_templates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    template_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # User sessions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT UNIQUE NOT NULL,
                    current_patient_code TEXT,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Data audit trail table for tracking changes
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS audit_trail (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    patient_code TEXT NOT NULL,
                    action TEXT NOT NULL,
                    old_data TEXT,
                    new_data TEXT,
                    user_id TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            logger.info("All database tables created successfully")
            
        except Exception as e:
            logger.error(f"Database table creation failed: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()
    
    def save_patient(self, patient_data: Dict[str, Any]) -> bool:
        """Save patient data to database"""
        logger.info(f"Attempting to save patient {patient_data.get('patient_code', 'UNKNOWN')}")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Convert complex objects to JSON strings
            patient_json = {
                'demographics': json.dumps(patient_data.get('demographics', {})),
                'present_illness': json.dumps(patient_data.get('present_illness', {})),
                'risk_assessment': json.dumps(patient_data.get('risk_assessment', {})),
                'psychiatric_history': json.dumps(patient_data.get('psychiatric_history', {})),
                'substance_history': json.dumps(patient_data.get('substance_history', {})),
                'medication_history': json.dumps(patient_data.get('medication_history', {})),
                'medical_history': json.dumps(patient_data.get('medical_history', {})),
                'mse': json.dumps(patient_data.get('mse', {})),
                'paraclinicals': json.dumps(patient_data.get('paraclinicals', {})),
                'diagnosis': json.dumps(patient_data.get('diagnosis', [])),
                'rating_scales': json.dumps(patient_data.get('rating_scales', {}))
            }
            
            # Validate JSON serialization
            for key, value in patient_json.items():
                try:
                    json.loads(value)
                except json.JSONDecodeError as e:
                    logger.error(f"JSON serialization failed for {key}: {e}")
                    return False
            
            # Check if patient exists
            cursor.execute('SELECT id FROM patients WHERE patient_code = ?', 
                          (patient_data['patient_code'],))
            existing = cursor.fetchone()
            
            if existing:
                # Update existing patient
                logger.info(f"Updating existing patient {patient_data['patient_code']}")
                cursor.execute('''
                    UPDATE patients SET 
                        demographics = ?, present_illness = ?, risk_assessment = ?,
                        psychiatric_history = ?, substance_history = ?, medication_history = ?,
                        medical_history = ?, mse = ?, paraclinicals = ?, diagnosis = ?,
                        rating_scales = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE patient_code = ?
                ''', (*patient_json.values(), patient_data['patient_code']))
            else:
                # Insert new patient
                logger.info(f"Creating new patient {patient_data['patient_code']}")
                cursor.execute('''
                    INSERT INTO patients (
                        patient_code, demographics, present_illness, risk_assessment,
                        psychiatric_history, substance_history, medication_history,
                        medical_history, mse, paraclinicals, diagnosis, rating_scales
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (patient_data['patient_code'], *patient_json.values()))
            
            conn.commit()
            logger.info(f"Patient {patient_data['patient_code']} saved successfully")
            return True
            
        except sqlite3.IntegrityError as e:
            logger.error(f"Database integrity error: {e}")
            st.error(f"Database integrity error: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Database save error: {e}")
            st.error(f"Database error: {str(e)}")
            return False
        finally:
            try:
                conn.close()
            except:
                pass
    
    def load_patient(self, patient_code: str) -> Optional[Dict[str, Any]]:
        """Load patient data from database"""
        logger.info(f"Loading patient {patient_code}")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM patients WHERE patient_code = ?', (patient_code,))
            result = cursor.fetchone()
            
            if result:
                logger.info(f"Patient {patient_code} found in database")
                columns = [desc[0] for desc in cursor.description]
                patient_data = dict(zip(columns, result))
                
                # Parse JSON fields
                json_fields = ['demographics', 'present_illness', 'risk_assessment',
                              'psychiatric_history', 'substance_history', 'medication_history',
                              'medical_history', 'mse', 'paraclinicals', 'diagnosis', 'rating_scales']
                
                for field in json_fields:
                    if patient_data[field]:
                        try:
                            patient_data[field] = json.loads(patient_data[field])
                        except json.JSONDecodeError as e:
                            logger.error(f"JSON parsing failed for field {field}: {e}")
                            # Data migration/repair functionality
                            patient_data[field] = {} if field != 'diagnosis' else []
                    else:
                        patient_data[field] = {} if field != 'diagnosis' else []
                
                return patient_data
            else:
                logger.info(f"Patient {patient_code} not found in database")
                return None
                
        except Exception as e:
            logger.error(f"Database load error: {e}")
            st.error(f"Database error: {str(e)}")
            return None
        finally:
            try:
                conn.close()
            except:
                pass
    
    def get_all_patients(self) -> List[Dict[str, Any]]:
        """Get all patients from database"""
        # Performance monitoring for large datasets
        start_time = time.time()
        logger.info("Loading all patients")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Pagination for large datasets
            # Search/filter capabilities
            cursor.execute('SELECT patient_code, created_at, updated_at FROM patients ORDER BY updated_at DESC')
            results = cursor.fetchall()
            
            patients = []
            for row in results:
                patients.append({
                    'patient_code': row[0],
                    'created_at': row[1],
                    'updated_at': row[2]
                })
            
            elapsed_time = time.time() - start_time
            logger.info(f"Loaded {len(patients)} patients in {elapsed_time:.2f} seconds")
            
            return patients
            
        except Exception as e:
            logger.error(f"Failed to load all patients: {e}")
            st.error(f"Database error: {str(e)}")
            return []
        finally:
            try:
                conn.close()
            except:
                pass
    
    def delete_patient(self, patient_code: str) -> bool:
        """Delete patient from database"""
        # Confirmation and audit trail
        logger.info(f"Deleting patient {patient_code}")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Soft delete functionality instead of hard delete
            cursor.execute('UPDATE patients SET deleted_at = CURRENT_TIMESTAMP WHERE patient_code = ?', (patient_code,))
            deleted_rows = cursor.rowcount
            
            if deleted_rows > 0:
                logger.info(f"Patient {patient_code} deleted successfully")
                conn.commit()
                return True
            else:
                logger.info(f"Patient {patient_code} not found for deletion")
                return False
                
        except Exception as e:
            logger.error(f"Failed to delete patient {patient_code}: {e}")
            st.error(f"Database error: {str(e)}")
            return False
        finally:
            try:
                conn.close()
            except:
                pass

# Data validation framework
class ValidationError(Exception):
    """Custom validation error"""
    pass

@dataclass
class ValidationResult:
    """Validation result data class"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]

class DataValidator:
    """Data validation utilities"""
    
    @staticmethod
    def validate_patient_code(code: str) -> List[str]:
        """Validate patient code format"""
        # More comprehensive validation rules
        logger.info(f"Validating patient code: {code}")
        
        errors = []
        if not code or not code.strip():
            errors.append("Patient code is required")
        elif len(code.strip()) < 3:
            errors.append("Patient code must be at least 3 characters")
        elif not re.match(r'^[A-Za-z0-9_-]+$', code.strip()):
            errors.append("Patient code can only contain letters, numbers, hyphens, and underscores")
        
        # Additional validation rules
        elif len(code.strip()) > 50:
            errors.append("Patient code must be less than 50 characters")
        elif code.strip().lower() in ['admin', 'test', 'demo']:
            errors.append("Patient code cannot be a reserved word")
        
        return errors
    
    @staticmethod
    def validate_age(age: str) -> List[str]:
        """Validate age input"""
        errors = []
        if not age:
            return errors  # Age is optional
        
        try:
            age_int = int(age)
            if age_int < 0 or age_int > 120:
                errors.append("Age must be between 0 and 120")
            # Age-specific warnings for pediatric/geriatric populations
            if age_int < 18:
                logger.warning("Pediatric patient - consider specialized assessment tools")
            elif age_int > 65:
                logger.warning("Geriatric patient - consider cognitive screening")
        except ValueError:
            errors.append("Age must be a valid number")
        
        return errors
    
    @staticmethod
    def validate_date(date_str: str, allow_future: bool = False) -> List[str]:
        """Validate date input"""
        errors = []
        if not date_str:
            return errors
        
        try:
            parsed_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            if not allow_future and parsed_date > date.today():
                errors.append("Date cannot be in the future")
            # Reasonable date range validation
            if parsed_date < date(1900, 1, 1):
                errors.append("Date cannot be before 1900")
        except ValueError:
            errors.append("Invalid date format. Use YYYY-MM-DD")
        
        return errors
    
    @staticmethod
    def validate_medication_dosage(dosage: str) -> List[str]:
        """Validate medication dosage format"""
        errors = []
        if not dosage:
            return errors
        
        # More sophisticated dosage validation
        # Basic dosage format validation (e.g., "10mg", "500mg twice daily")
        if not re.match(r'^\d+(\.\d+)?\s*(mg|g|mcg|ml|units)?(\s+.*)?$', dosage.strip()):
            errors.append("Invalid dosage format. Use format like '10mg' or '500mg twice daily'")
        
        # Drug-specific dosage validation
        # Unit conversion validation
        # Dangerous dosage warnings
        
        return errors
    
    @staticmethod
    def validate_patient_data(patient: Dict[str, Any], existing_codes: List[str] = None) -> ValidationResult:
        """Comprehensive patient data validation"""
        logger.info("Starting comprehensive patient validation")
        
        errors = []
        warnings = []
        
        # Validate patient code
        code_errors = DataValidator.validate_patient_code(patient.get('patient_code', ''))
        errors.extend(code_errors)
        
        # Check for duplicate codes
        if existing_codes and patient.get('patient_code') in existing_codes:
            errors.append("Patient code already exists")
        
        # Validate demographics
        demographics = patient.get('demographics', {})
        age_errors = DataValidator.validate_age(demographics.get('age', ''))
        errors.extend(age_errors)
        
        # Validate dates in psychiatric history
        psych_history = patient.get('psychiatric_history', {})
        for date_field in ['first_hospitalization', 'last_hospitalization']:
            date_errors = DataValidator.validate_date(psych_history.get(date_field, ''))
            errors.extend(date_errors)
        
        # Validate suicide attempts date
        suicide_attempts = psych_history.get('suicide_attempts', {})
        if suicide_attempts:
            date_errors = DataValidator.validate_date(suicide_attempts.get('most_recent', ''))
            errors.extend(date_errors)
        
        # Validate medication dosages
        med_history = patient.get('medication_history', {})
        for med_list in ['current_medications', 'previous_medications']:
            for med in med_history.get(med_list, []):
                if isinstance(med, dict) and 'dosage' in med:
                    dosage_errors = DataValidator.validate_medication_dosage(med['dosage'])
                    errors.extend(dosage_errors)
        
        # Cross-field validation
        # Example: If suicide attempt history exists, ensure risk assessment is completed
        if psych_history.get('suicide_attempts', {}).get('history'):
            risk_assessment = patient.get('risk_assessment', {})
            if not risk_assessment.get('suicidal', {}).get('present'):
                warnings.append("History of suicide attempts but current suicidal risk not assessed")
        
        # Add warnings for incomplete data
        if not patient.get('diagnosis'):
            warnings.append("No diagnosis provided")
        
        risk_assessment = patient.get('risk_assessment', {})
        if not risk_assessment.get('suicidal', {}).get('present') and not risk_assessment.get('homicidal', {}).get('present'):
            warnings.append("Risk assessment not completed")
        
        # Data completeness scoring
        completeness_score = DataValidator.calculate_data_completeness(patient)
        if completeness_score < 0.5:
            warnings.append(f"Data only {completeness_score:.1%} complete")
        
        logger.info(f"Validation complete - {len(errors)} errors, {len(warnings)} warnings")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    @staticmethod
    def calculate_data_completeness(patient: Dict[str, Any]) -> float:
        """Calculate data completeness score"""
        total_fields = 0
        completed_fields = 0
        
        # Count demographics fields
        demographics = patient.get('demographics', {})
        total_fields += 3  # age, gender, occupation
        completed_fields += sum(1 for field in demographics.values() if field)
        
        # Count present illness symptoms
        present_illness = patient.get('present_illness', {})
        total_symptoms = sum(len(symptoms) for symptoms in SYMPTOM_DICTIONARY.values())
        present_symptoms = sum(1 for symptoms in present_illness.values() 
                             for value in symptoms.values() if value)
        total_fields += total_symptoms
        completed_fields += present_symptoms
        
        # Count risk assessment fields
        risk_assessment = patient.get('risk_assessment', {})
        total_fields += 6  # present, severity, details for both suicidal and homicidal
        completed_fields += sum(1 for risk_type in ['suicidal', 'homicidal'] 
                              for field in ['present', 'severity', 'details'] 
                              if risk_assessment.get(risk_type, {}).get(field))
        
        # Count MSE fields
        mse = patient.get('mse', {})
        mse_fields = ['appearance', 'behavior', 'speech', 'mood', 'affect', 'thought_process', 
                     'thought_content', 'perception', 'cognition', 'insight', 'judgment', 'orientation']
        total_fields += len(mse_fields)
        completed_fields += sum(1 for field in mse_fields if mse.get(field))
        
        return completed_fields / total_fields if total_fields > 0 else 0

# Data standardization for ML
class DataStandardizer:
    """Data standardization utilities for ML readiness"""
    
    @staticmethod
    def standardize_categorical_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """Standardize categorical variables"""
        logger.info("Standardizing categorical data")
        
        standardized = data.copy()
        
        # Standardize gender
        if 'demographics' in standardized:
            gender = standardized['demographics'].get('gender', '').lower()
            if gender in ['m', 'male', 'man']:
                standardized['demographics']['gender'] = 'Male'
            elif gender in ['f', 'female', 'woman']:
                standardized['demographics']['gender'] = 'Female'
            elif gender in ['other', 'non-binary', 'nb']:
                standardized['demographics']['gender'] = 'Other'
            # Additional gender categories as needed
            elif gender in ['prefer_not_to_say', 'unknown']:
                standardized['demographics']['gender'] = 'Unknown'
        
        # Standardize risk severity levels
        if 'risk_assessment' in standardized:
            for risk_type in ['suicidal', 'homicidal']:
                severity = standardized['risk_assessment'][risk_type].get('severity', '').lower()
                if severity in ['low', 'mild']:
                    standardized['risk_assessment'][risk_type]['severity'] = 'Low'
                elif severity in ['moderate', 'medium']:
                    standardized['risk_assessment'][risk_type]['severity'] = 'Moderate'
                elif severity in ['high', 'severe']:
                    standardized['risk_assessment'][risk_type]['severity'] = 'High'
                elif severity in ['imminent', 'urgent']:
                    standardized['risk_assessment'][risk_type]['severity'] = 'Imminent'
        
        # Standardize medication names using drug database
        # Standardize diagnosis codes (ICD-10/DSM-5)
        # Standardize lab values with reference ranges
        
        logger.info("Categorical data standardization complete")
        return standardized
    
    @staticmethod
    def encode_symptoms(patient_data: Dict[str, Any]) -> Dict[str, int]:
        """Encode symptoms as binary features"""
        logger.info("Encoding symptoms for ML")
        
        encoded = {}
        
        present_illness = patient_data.get('present_illness', {})
        for category, symptoms in present_illness.items():
            if isinstance(symptoms, dict):
                for symptom, value in symptoms.items():
                    key = f"{category}_{symptom.replace(' ', '_').lower()}"
                    encoded[key] = 1 if value else 0
        
        logger.info(f"Encoded {len(encoded)} symptom features")
        return encoded
    
    @staticmethod
    def handle_missing_values(df: pd.DataFrame) -> pd.DataFrame:
        """Handle missing values in dataset"""
        logger.info(f"Handling missing values in dataset with shape {df.shape}")
        
        # Separate numeric and categorical columns
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object']).columns
        
        logger.info(f"Found {len(numeric_cols)} numeric and {len(categorical_cols)} categorical columns")
        
        # Impute numeric columns with median
        if len(numeric_cols) > 0:
            numeric_imputer = SimpleImputer(strategy='median')
            df[numeric_cols] = numeric_imputer.fit_transform(df[numeric_cols])
            logger.info("Numeric columns imputed with median")
        
        # Impute categorical columns with mode
        if len(categorical_cols) > 0:
            categorical_imputer = SimpleImputer(strategy='most_frequent')
            df[categorical_cols] = categorical_imputer.fit_transform(df[categorical_cols])
            logger.info("Categorical columns imputed with mode")
        
        # More sophisticated imputation strategies
        # - KNN imputation for numeric data
        # - Custom imputation based on clinical knowledge
        # - Multiple imputation for uncertainty quantification
        
        return df
    
    @staticmethod
    def normalize_features(df: pd.DataFrame, exclude_cols: List[str] = None) -> pd.DataFrame:
        """Normalize numeric features"""
        if exclude_cols is None:
            exclude_cols = []
        
        logger.info(f"Normalizing features, excluding {len(exclude_cols)} columns")
        
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        numeric_cols = [col for col in numeric_cols if col not in exclude_cols]
        
        if len(numeric_cols) > 0:
            scaler = StandardScaler()
            df[numeric_cols] = scaler.fit_transform(df[numeric_cols])
            logger.info(f"Normalized {len(numeric_cols)} numeric features")
        
        # Save scaler for future use in predictions
        with open('feature_scaler.pkl', 'wb') as f:
            pickle.dump(scaler, f)
        
        return df

# Rating scales for standardized assessments
class RatingScales:
    """Standardized psychiatric rating scales"""
    
    # More comprehensive rating scales
    # Scoring algorithms and interpretations
    # Scale-specific validation rules
    
    PHQ9_QUESTIONS = [
        "Little interest or pleasure in doing things",
        "Feeling down, depressed, or hopeless",
        "Trouble falling or staying asleep, or sleeping too much",
        "Feeling tired or having little energy",
        "Poor appetite or overeating",
        "Feeling bad about yourself—or that you are a failure or have let yourself or your family down",
        "Trouble concentrating on things, such as reading the newspaper or watching television",
        "Moving or speaking so slowly that other people could have noticed. Or the opposite—being so fidgety or restless that you have been moving around a lot more than usual",
        "Thoughts that you would be better off dead, or of hurting yourself"
    ]
    
    GAD7_QUESTIONS = [
        "Feeling nervous, anxious or on edge",
        "Not being able to stop or control worrying",
        "Worrying too much about different things",
        "Trouble relaxing",
        "Being so restless that it is hard to sit still",
        "Becoming easily annoyed or irritable",
        "Feeling afraid as if something awful might happen"
    ]
    
    BPRS_QUESTIONS = [
        "Somatic concern",
        "Anxiety",
        "Emotional withdrawal",
        "Conceptual disorganization",
        "Guilt feelings",
        "Tension",
        "Mannerisms and posturing",
        "Grandiosity",
        "Depressive mood",
        "Hostility",
        "Suspiciousness",
        "Hallucinatory behavior",
        "Motor retardation",
        "Uncooperativeness",
        "Unusual thought content",
        "Blunted affect",
        "Excitement",
        "Disorientation",
        "Motor hyperactivity"
    ]
    
    # Additional rating scales
    MMSE_QUESTIONS = [
        "Orientation to time",
        "Orientation to place",
        "Registration",
        "Attention and calculation",
        "Recall",
        "Language"
    ]
    
    YALE_BROWN_QUESTIONS = [
        "Time occupied by obsessive thoughts",
        "Interference due to obsessive thoughts",
        "Distress associated with obsessive thoughts",
        "Resistance against obsessions",
        "Degree of control over obsessions",
        "Time spent performing compulsive behaviors",
        "Interference due to compulsive behaviors",
        "Distress associated with compulsive behavior",
        "Resistance against compulsions",
        "Degree of control over compulsive behavior"
    ]

# Initialize database
# Connection pooling and error handling for database initialization
try:
    db_manager = DatabaseManager()
    logger.info("Database manager initialized successfully")
except Exception as e:
    logger.critical(f"Database manager initialization failed: {e}")
    st.error("Database initialization failed. Please check your setup.")
    st.stop()

# Initialize session state
def initialize_session_state():
    """Initialize session state variables"""
    logger.info("Initializing session state")
    
    # Session state persistence across browser sessions
    # User authentication and session management
    
    if 'patients' not in st.session_state:
        st.session_state.patients = db_manager.get_all_patients()
        logger.info(f"Loaded {len(st.session_state.patients)} patients into session state")
    
    if 'current_patient' not in st.session_state:
        st.session_state.current_patient = create_empty_patient()
        logger.info("Created empty patient in session state")
    
    if 'last_autosave' not in st.session_state:
        st.session_state.last_autosave = time.time()
        logger.info("Initialized autosave timer")
    
    if 'session_id' not in st.session_state:
        st.session_state.session_id = str(uuid.uuid4())
        logger.info(f"Generated session ID: {st.session_state.session_id}")
    
    if 'validation_errors' not in st.session_state:
        st.session_state.validation_errors = []
    
    if 'validation_warnings' not in st.session_state:
        st.session_state.validation_warnings = []
    
    # Additional session state variables
    if 'user_preferences' not in st.session_state:
        st.session_state.user_preferences = {}
    if 'debug_mode' not in st.session_state:
        st.session_state.debug_mode = False

def create_empty_patient() -> Dict[str, Any]:
    """Create an empty patient data structure"""
    logger.info("Creating empty patient structure")
    
    # Make this configurable based on assessment type
    # Version control for patient data structure
    
    return {
        'patient_code': '',
        'demographics': {'age': '', 'gender': '', 'occupation': ''},
        'present_illness': {
            'mood': {}, 'bipolar': {}, 'anxiety': {}, 'psychotic': {},
            'cognitive': {}, 'somatic': {}, 'behavioral': {}, 'other': {}
        },
        'risk_assessment': {
            'suicidal': {'present': False, 'severity': '', 'details': '', 'ideation': False, 'plan': False, 
                        'intent': False, 'means': False, 'previous_attempts': False, 'impulsivity': False, 
                        'hopelessness': False, 'social_isolation': False, 'substance_use': False, 
                        'psychosis': False, 'command_hallucinations': False, 'poor_insight': False},
            'homicidal': {'present': False, 'severity': '', 'details': ''}
        },
        'psychiatric_history': {
            'total_hospitalizations': '',
            'first_hospitalization': '',
            'last_hospitalization': '',
            'involuntary_admissions': '',
            'suicide_attempts': {'history': False, 'number': '', 'most_recent': ''},
            'self_injury': {}
        },
        'substance_history': {
            substance: {'use': False, 'frequency': '', 'amount': '', 'problems': ''}
            for substance in ['alcohol', 'tobacco', 'cannabis', 'stimulants', 'opioids', 'benzodiazepines', 'other']
        },
        'medication_history': {
            'current_medications': [],
            'previous_medications': [],
            'allergies': '',
            'adherence': ''
        },
        'medical_history': {
            'conditions': {},
            'comments': ''
        },
        'mse': {
            'appearance': '', 'behavior': '', 'speech': '', 'mood': '', 'affect': '',
            'thought_process': '', 'thought_content': '', 'perception': '',
            'cognition': '', 'insight': '', 'judgment': '', 'orientation': ''
        },
        'paraclinicals': {
            'cbc': {}, 'lft': {}, 'tft': {}, 'lipids': {}, 'electrolytes': {}, 'other': {}
        },
        'diagnosis': [],
        'rating_scales': {
            'phq9': {},
            'gad7': {},
            'bprs': {},
            'mmse': {},
            'yale_brown': {}
        },
        'last_modified': datetime.now().isoformat(),
        # Additional fields for ML features
        'calculated_scores': {},  # Store calculated assessment scores
        'risk_factors': {},       # Extracted risk factors for ML
        'data_version': '1.0'     # Track data structure version
    }

# Assessment templates
# Make templates configurable and user-editable
# Template validation and versioning
ASSESSMENT_TEMPLATES = {
    'Depression': {
        'present_illness': {
            'mood': {'Depressed mood': True, 'Anhedonia': True, 'Fatigue': True, 'Worthlessness': True, 
                    'Concentration problems': True, 'Appetite loss': True, 'Sleep cycle reversal': True}
        },
        'risk_assessment': {
            'suicidal': {'present': True, 'severity': 'moderate', 'ideation': True, 'hopelessness': True}
        },
        'rating_scales': {
            'phq9': {q: 2 for q in RatingScales.PHQ9_QUESTIONS[:7]}  # Moderate depression
        },
        # Typical medication patterns, risk factors, etc.
    },
    'Bipolar Disorder': {
        'present_illness': {
            'bipolar': {'Elevated mood': True, 'Decreased need for sleep': True, 'Racing thoughts': True, 
                       'Grandiosity': True, 'Pressured speech': True}
        }
    },
    'Anxiety Disorder': {
        'present_illness': {
            'anxiety': {'Excessive worry': True, 'Restlessness': True, 'Muscle tension': True, 
                       'Panic attacks': True, 'Palpitations': True}
        },
        'rating_scales': {
            'gad7': {q: 2 for q in RatingScales.GAD7_QUESTIONS[:5]}  # Moderate anxiety
        }
    },
    'Psychotic Disorder': {
        'present_illness': {
            'psychotic': {'Delusions': True, 'Auditory hallucinations': True, 'Disorganized speech': True, 
                         'Paranoid ideation': True}
        },
        'rating_scales': {
            'bprs': {q: 4 for q in RatingScales.BPRS_QUESTIONS[:10]}  # Moderate psychosis
        }
    },
    'OCD': {
        'present_illness': {
            'anxiety': {'Obsessions': True, 'Compulsions': True, 'Anxiety': True}
        },
        'rating_scales': {
            'yale_brown': {q: 3 for q in RatingScales.YALE_BROWN_QUESTIONS[:5]}  # Moderate OCD
        }
    },
    'Dementia': {
        'present_illness': {
            'cognitive': {'Memory impairment': True, 'Executive dysfunction': True, 'Orientation problems': True}
        },
        'rating_scales': {
            'mmse': {'Orientation to time': 2, 'Orientation to place': 2, 'Registration': 3}
        }
    }
    # More templates - ADHD, PTSD, Eating Disorders, etc.
}

# Symptom dictionaries
# Make symptom dictionaries configurable and expandable
# Add symptom severity levels and duration tracking
SYMPTOM_DICTIONARY = {
    'mood': {
        'Depressed mood': 'dep', 'Anhedonia': 'anh', 'Insomnia': 'ins',
        'Sleep cycle reversal': 'scr', 'Hypersomnia': 'hyp', 'Fatigue': 'fat',
        'Worthlessness': 'worth', 'Guilt': 'guilt', 'Concentration problems': 'conc',
        'Appetite loss': 'app-', 'Appetite increase': 'app+', 'Weight loss': 'wt-',
        'Weight gain': 'wt+', 'Psychomotor retardation': 'pmr', 'Psychomotor agitation': 'pma',
        'Death ideation': 'death', 'Suicidal ideation': 'si', 'Hopelessness': 'hope',
        'Irritability': 'irr', 'Crying spells': 'cry',
        # More mood symptoms - anhedonia subtypes, mood variability, etc.
        'Mood lability': 'labil', 'Anger outbursts': 'anger', 'Emotional numbness': 'numb'
    },
    'bipolar': {
        'Elevated mood': 'elev', 'Euphoria': 'euph', 'Grandiosity': 'grand',
        'Decreased need for sleep': 'dns', 'Racing thoughts': 'race', 'Flight of ideas': 'foi',
        'Distractibility': 'dist', 'Increased goal-directed activity': 'goal',
        'Hypersexuality': 'sex', 'Excessive spending': 'spend', 'Poor judgment': 'judge',
        'Pressured speech': 'press', 'Rapid speech': 'rapid', 'Hyperactivity': 'hyper',
        'Mixed features': 'mixed', 'Dysphoric mania': 'dysp',
        # Hypomania vs mania distinction, cycling patterns
        'Irritability (manic)': 'irr_manic', 'Reckless behavior': 'reck'
    },
    'anxiety': {
        'Excessive worry': 'worry', 'Restlessness': 'rest', 'Muscle tension': 'tense',
        'Panic attacks': 'panic', 'Palpitations': 'palp', 'Sweating': 'sweat',
        'Trembling': 'shake', 'Shortness of breath': 'sob', 'Avoidance': 'avoid',
        'Social anxiety': 'social', 'Agoraphobia': 'agora', 'Specific phobias': 'phobia',
        'Anticipatory anxiety': 'antic', 'Chest pain': 'chest', 'Dizziness': 'dizzy',
        'Nausea': 'nausea',
        # Anxiety disorder subtypes, trigger patterns
        'Obsessions': 'obs', 'Compulsions': 'comp', 'Health anxiety': 'health'
    },
    'psychotic': {
        'Delusions': 'del', 'Auditory hallucinations': 'ah', 'Visual hallucinations': 'vh',
        'Command hallucinations': 'cmd', 'Thought broadcasting': 'tb', 'Thought insertion': 'ti',
        'Thought withdrawal': 'tw', 'Disorganized speech': 'ds', 'Disorganized behavior': 'db',
        'Catatonia': 'cat', 'Flat affect': 'flat', 'Alogia': 'alo', 'Avolition': 'avol',
        'Paranoid ideation': 'para', 'Ideas of reference': 'ref', 'Bizarre behavior': 'bizarre',
        # Positive vs negative symptoms, insight levels
        'Negative symptoms': 'neg', 'Formal thought disorder': 'ftd'
    },
    'cognitive': {
        'Memory impairment': 'mem', 'Executive dysfunction': 'exec', 'Attention deficit': 'att',
        'Processing speed': 'slow', 'Language problems': 'lang', 'Orientation problems': 'orient',
        'Abstract thinking': 'abstract', 'Calculation problems': 'calc', 'Visuospatial problems': 'visual',
        # Specific cognitive domains, severity levels
        'Working memory deficit': 'wm', 'Learning difficulties': 'learn'
    },
    'somatic': {
        'Headaches': 'head', 'Back pain': 'back', 'Chest pain': 'chest',
        'Abdominal pain': 'abd', 'Joint pain': 'joint', 'Fatigue': 'tired',
        'Dizziness': 'dizzy', 'Nausea': 'nausea', 'Palpitations': 'palp',
        'Shortness of breath': 'breath',
        # Pain scales, functional impairment measures
        'Chronic pain': 'chronic', 'Sleep disturbances': 'sleep_dist'
    },
    'behavioral': {
        'Aggression': 'agg', 'Self-harm': 'sh', 'Substance use': 'sub',
        'Risky behavior': 'risk', 'Social withdrawal': 'with', 'Hypervigilance': 'vigil',
        'Compulsions': 'comp', 'Obsessions': 'obs', 'Tics': 'tics',
        'Repetitive behaviors': 'rep',
        # Behavioral frequency, triggers, interventions
        'Impulsivity': 'impulse', 'Compulsive buying': 'buy'
    }
}

# Add ICD-10/DSM-5 codes to medical conditions
MEDICAL_CONDITIONS = {
    'Hypothyroidism': {'code': 'hypo', 'icd10': 'E03.9'},
    'Hyperthyroidism': {'code': 'hyper', 'icd10': 'E05.9'},
    'Diabetes Mellitus': {'code': 'dm', 'icd10': 'E11.9'},
    'Hypertension': {'code': 'htn', 'icd10': 'I10'},
    'Seizure disorder': {'code': 'seizure', 'icd10': 'G40.9'},
    'Head trauma': {'code': 'trauma', 'icd10': 'S06.9'},
    'Stroke': {'code': 'stroke', 'icd10': 'I64.9'},
    'Heart disease': {'code': 'heart', 'icd10': 'I51.9'},
    'Kidney disease': {'code': 'kidney', 'icd10': 'N18.9'},
    'Liver disease': {'code': 'liver', 'icd10': 'K76.9'},
    # More conditions - autoimmune, neurological, endocrine
    'Autoimmune disorders': {'code': 'auto', 'icd10': 'M35.9'},
    'Vitamin deficiencies': {'code': 'vit', 'icd10': 'E56.9'},
    # Severity and treatment status
}

# Add reference ranges and units for lab tests
LAB_TESTS = {
    'cbc': {
        'WBC': {'range': (4.0, 11.0), 'unit': 'K/uL'},
        'RBC': {'range': (4.2, 5.4), 'unit': 'M/uL'},
        'Hgb': {'range': (12.0, 16.0), 'unit': 'g/dL'},
        'Hct': {'range': (36.0, 48.0), 'unit': '%'},
        'Platelets': {'range': (150, 450), 'unit': 'K/uL'},
        'Neutrophils': {'range': (40, 75), 'unit': '%'},
        'Lymphocytes': {'range': (20, 45), 'unit': '%'},
        'Monocytes': {'range': (2, 10), 'unit': '%'},
        'Eosinophils': {'range': (0, 5), 'unit': '%'},
        'Basophils': {'range': (0, 2), 'unit': '%'}
    },
    'lft': {
        'ALT': {'range': (7, 56), 'unit': 'U/L'},
        'AST': {'range': (10, 40), 'unit': 'U/L'},
        'ALP': {'range': (40, 129), 'unit': 'U/L'},
        'Bilirubin Total': {'range': (0.1, 1.2), 'unit': 'mg/dL'},
        'Bilirubin Direct': {'range': (0.0, 0.3), 'unit': 'mg/dL'},
        'Albumin': {'range': (3.5, 5.0), 'unit': 'g/dL'}
    },
    'tft': {
        'TSH': {'range': (0.4, 4.5), 'unit': 'mIU/L'},
        'T3': {'range': (80, 200), 'unit': 'ng/dL'},
        'T4': {'range': (4.5, 12.0), 'unit': 'mcg/dL'},
        'Free T4': {'range': (0.8, 1.8), 'unit': 'ng/dL'}
    },
    'lipids': {
        'Total Cholesterol': {'range': (<200), 'unit': 'mg/dL'},
        'HDL': {'range': (>=40), 'unit': 'mg/dL'},
        'LDL': {'range': (<100), 'unit': 'mg/dL'},
        'Triglycerides': {'range': (<150), 'unit': 'mg/dL'}
    },
    'electrolytes': {
        'Sodium': {'range': (135, 145), 'unit': 'mEq/L'},
        'Potassium': {'range': (3.5, 5.0), 'unit': 'mEq/L'},
        'Chloride': {'range': (98, 107), 'unit': 'mEq/L'},
        'CO2': {'range': (22, 30), 'unit': 'mEq/L'},
        'BUN': {'range': (7, 20), 'unit': 'mg/dL'},
        'Creatinine': {'range': (0.6, 1.3), 'unit': 'mg/dL'},
        'Glucose': {'range': (70, 100), 'unit': 'mg/dL'}
    },
    # Additional tests - vitamin levels, drug levels, inflammatory markers
    'vitamins': {
        'Vitamin D': {'range': (30, 100), 'unit': 'ng/mL'},
        'Vitamin B12': {'range': (200, 900), 'unit': 'pg/mL'},
        'Folate': {'range': (3, 17), 'unit': 'ng/mL'}
    },
    'inflammation': {
        'CRP': {'range': (<3.0), 'unit': 'mg/L'},
        'ESR': {'range': (0, 20), 'unit': 'mm/hr'}
    }
}

# Add structured MSE assessment with scoring
MSE_OPTIONS = {
    'appearance': ['Well-groomed', 'Disheveled', 'Bizarre dress', 'Poor hygiene', 'Appropriate'],
    'behavior': ['Cooperative', 'Agitated', 'Withdrawn', 'Bizarre', 'Restless', 'Psychomotor retardation'],
    'speech': ['Normal rate/rhythm', 'Pressured', 'Slow', 'Loud', 'Whispered', 'Circumstantial', 'Tangential'],
    'mood': ['Euthymic', 'Depressed', 'Elevated', 'Irritable', 'Anxious', 'Angry'],
    'affect': ['Appropriate', 'Flat', 'Blunted', 'Labile', 'Restricted', 'Inappropriate'],
    'thought_process': ['Linear', 'Circumstantial', 'Tangential', 'Flight of ideas', 'Loose associations'],
    'thought_content': ['No abnormalities', 'Delusions', 'Obsessions', 'Phobias', 'Suicidal ideation'],
    'perception': ['No abnormalities', 'Auditory hallucinations', 'Visual hallucinations', 'Tactile hallucinations'],
    'cognition': ['Intact', 'Mild impairment', 'Moderate impairment', 'Severe impairment', 'MMSE score', 'MoCA score'],
    'insight': ['Good', 'Fair', 'Poor', 'Absent'],
    'judgment': ['Good', 'Fair', 'Poor', 'Impaired'],
    'orientation': ['Oriented x3', 'Oriented x2', 'Oriented x1', 'Disoriented'],
    # Detailed cognitive assessment options
    'memory': ['Intact', 'Mild impairment', 'Moderate impairment', 'Severe impairment'],
    'attention': ['Intact', 'Mild impairment', 'Moderate impairment', 'Severe impairment'],
    'executive_function': ['Intact', 'Mild impairment', 'Moderate impairment', 'Severe impairment']
}

def save_patient():
    """Save current patient to database"""
    logger.info("Attempting to save current patient")
    
    # Validate data
    existing_codes = [p['patient_code'] for p in st.session_state.patients 
                     if p['patient_code'] != st.session_state.current_patient['patient_code']]
    
    validation_result = DataValidator.validate_patient_data(
        st.session_state.current_patient, existing_codes
    )
    
    st.session_state.validation_errors = validation_result.errors
    st.session_state.validation_warnings = validation_result.warnings
    
    if validation_result.errors:
        logger.warning(f"Validation errors found: {len(validation_result.errors)}")
        for error in validation_result.errors:
            st.error(error)
        return False
    
    if validation_result.warnings:
        logger.warning(f"Validation warnings found: {len(validation_result.warnings)}")
        for warning in validation_result.warnings:
            st.warning(warning)
    
    # Standardize data before saving
    standardized_patient = DataStandardizer.standardize_categorical_data(
        st.session_state.current_patient
    )
    
    # Calculate derived features for ML
    standardized_patient['calculated_scores'] = calculate_ml_features(standardized_patient)
    
    # Save to database
    if db_manager.save_patient(standardized_patient):
        st.session_state.current_patient['last_modified'] = datetime.now().isoformat()
        
        # Update patient list
        st.session_state.patients = db_manager.get_all_patients()
        
        st.success("Patient data saved successfully!")
        logger.info("Patient saved successfully")
        return True
    else:
        logger.error("Patient save failed")
        return False

def load_patient(patient_code: str):
    """Load patient data from database"""
    logger.info(f"Loading patient {patient_code} into session")
    
    patient_data = db_manager.load_patient(patient_code)
    if patient_data:
        st.session_state.current_patient = patient_data
        st.success(f"Loaded patient: {patient_code}")
        logger.info(f"Patient {patient_code} loaded successfully")
        st.rerun()
    else:
        st.error(f"Patient not found: {patient_code}")
        logger.warning(f"Patient {patient_code} not found")

def autosave_patient():
    """Autosave patient data if changes have been made"""
    current_time = time.time()
    # Make autosave interval configurable
    if current_time - st.session_state.last_autosave > 30:  # Autosave every 30 seconds
        if st.session_state.current_patient['patient_code'].strip():
            logger.info("Performing autosave")
            if save_patient():
                st.session_state.last_autosave = current_time
                logger.info("Autosave successful")
            else:
                logger.warning("Autosave failed")

def export_data(format_type: str, ml_ready: bool = False):
    """Export patient data in various formats"""
    logger.info(f"Exporting data as {format_type}, ML-ready: {ml_ready}")
    
    if not st.session_state.patients:
        st.warning("No patient data to export!")
        return
    
    # Add progress bar for large exports
    # Export filtering options
    patients_data = []
    for patient_info in st.session_state.patients:
        patient_data = db_manager.load_patient(patient_info['patient_code'])
        if patient_data:
            patients_data.append(patient_data)
    
    if not patients_data:
        st.warning("No patient data available for export!")
        return
    
    logger.info(f"Exporting {len(patients_data)} patients")
    
    if format_type == 'json':
        data = json.dumps(patients_data, indent=2)
        st.download_button(
            label="Download JSON",
            data=data,
            file_name=f"psychiatric_data_{datetime.now().strftime('%Y%m%d')}.json",
            mime="application/json"
        )
        logger.info("JSON export completed")
    
    elif format_type == 'csv':
        # Flatten data for ML
        logger.info("Flattening data for CSV export")
        flattened_data = []
        for patient in patients_data:
            flat = {
                'patient_code': patient['patient_code'],
                'age': patient['demographics'].get('age', ''),
                'gender': patient['demographics'].get('gender', ''),
                'occupation': patient['demographics'].get('occupation', ''),
            }
            
            # Flatten symptoms
            for category, symptoms in patient['present_illness'].items():
                for symptom, value in symptoms.items():
                    flat[f"{category}_{symptom.replace(' ', '_').lower()}"] = value
            
            # Risk assessment
            flat['suicidal_ideation'] = patient['risk_assessment']['suicidal'].get('present', False)
            flat['suicidal_severity'] = patient['risk_assessment']['suicidal'].get('severity', '')
            flat['homicidal_ideation'] = patient['risk_assessment']['homicidal'].get('present', False)
            
            # Medical conditions
            for condition, value in patient['medical_history']['conditions'].items():
                flat[f"medical_{condition.replace(' ', '_').lower()}"] = value
            
            # MSE
            for category, value in patient['mse'].items():
                flat[f"mse_{category}"] = value
            
            # Lab tests
            for category, tests in patient['paraclinicals'].items():
                if isinstance(tests, dict):
                    for test, value in tests.items():
                        flat[f"lab_{category}_{test.replace(' ', '_').lower()}"] = value
            
            # Rating scales
            for scale, questions in patient.get('rating_scales', {}).items():
                if isinstance(questions, dict):
                    for question, score in questions.items():
                        flat[f"{scale}_{question.replace(' ', '_').lower()}"] = score
            
            # Additional ML features
            flat['days_since_last_hospitalization'] = calculate_days_since_hospitalization(patient)
            flat['medication_count'] = len(patient['medication_history']['current_medications'])
            flat['comorbidity_count'] = sum(patient['medical_history']['conditions'].values())
            
            flattened_data.append(flat)
        
        df = pd.DataFrame(flattened_data)
        
        if ml_ready:
            logger.info("Applying ML preprocessing")
            # Apply ML preprocessing
            df = DataStandardizer.handle_missing_values(df)
            df = DataStandardizer.normalize_features(df, exclude_cols=['patient_code'])
            
            # Feature engineering
            df = add_derived_features(df)
            df = remove_low_variance_features(df)
        
        # Handle None/empty values
        df = df.fillna('')
        csv = df.to_csv(index=False)
        
        filename = f"psychiatric_data_{'ml_ready_' if ml_ready else ''}{datetime.now().strftime('%Y%m%d')}.csv"
        st.download_button(
            label=f"Download {'ML-Ready ' if ml_ready else ''}CSV",
            data=csv,
            file_name=filename,
            mime="text/csv"
        )
        logger.info(f"CSV export completed, {len(df)} rows, {len(df.columns)} columns")
    
    elif format_type == 'excel':
        logger.info("Creating Excel export with multiple sheets")
        # Create Excel file with multiple sheets
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            # Main data sheet
            flattened_data = []
            for patient in patients_data:
                flat = {
                    'patient_code': patient['patient_code'],
                    'age': patient['demographics'].get('age', ''),
                    'gender': patient['demographics'].get('gender', ''),
                    'occupation': patient['demographics'].get('occupation', ''),
                }
                flattened_data.append(flat)
            
            df_main = pd.DataFrame(flattened_data)
            df_main.to_excel(writer, sheet_name='Patients', index=False)
            
            # Symptoms sheet
            symptoms_data = []
            for patient in patients_data:
                for category, symptoms in patient['present_illness'].items():
                    for symptom, value in symptoms.items():
                        symptoms_data.append({
                            'patient_code': patient['patient_code'],
                            'category': category,
                            'symptom': symptom,
                            'present': value
                        })
            
            if symptoms_data:
                df_symptoms = pd.DataFrame(symptoms_data)
                df_symptoms.to_excel(writer, sheet_name='Symptoms', index=False)
            
            # Risk assessment sheet
            risk_data = []
            for patient in patients_data:
                risk_data.append({
                    'patient_code': patient['patient_code'],
                    'suicidal_present': patient['risk_assessment']['suicidal'].get('present', False),
                    'suicidal_severity': patient['risk_assessment']['suicidal'].get('severity', ''),
                    'homicidal_present': patient['risk_assessment']['homicidal'].get('present', False),
                    'homicidal_severity': patient['risk_assessment']['homicidal'].get('severity', '')
                })
            
            if risk_data:
                df_risk = pd.DataFrame(risk_data)
                df_risk.to_excel(writer, sheet_name='Risk Assessment', index=False)
            
            # Additional sheets for medications, lab results, etc.
            # Medications sheet
            medications_data = []
            for patient in patients_data:
                for med in patient['medication_history']['current_medications']:
                    medications_data.append({
                        'patient_code': patient['patient_code'],
                        'name': med.get('name', ''),
                        'dosage': med.get('dosage', ''),
                        'frequency': med.get('frequency', ''),
                        'indication': med.get('indication', ''),
                        'type': 'current'
                    })
                for med in patient['medication_history']['previous_medications']:
                    medications_data.append({
                        'patient_code': patient['patient_code'],
                        'name': med.get('name', ''),
                        'dosage': med.get('dosage', ''),
                        'duration': med.get('duration', ''),
                        'reason': med.get('reason', ''),
                        'type': 'previous'
                    })
            
            if medications_data:
                df_medications = pd.DataFrame(medications_data)
                df_medications.to_excel(writer, sheet_name='Medications', index=False)
        
        output.seek(0)
        st.download_button(
            label="Download Excel",
            data=output,
            file_name=f"psychiatric_data_{datetime.now().strftime('%Y%m%d')}.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        logger.info("Excel export completed")

def search_symptoms(search_term: str) -> Dict[str, List[str]]:
    """Search for symptoms across all categories"""
    if not search_term:
        return {}
    
    logger.info(f"Searching for symptoms matching '{search_term}'")
    
    results = {}
    search_term = search_term.lower()
    
    for category, symptoms in SYMPTOM_DICTIONARY.items():
        matches = []
        for symptom, code in symptoms.items():
            if search_term in symptom.lower() or search_term == code.lower():
                matches.append(symptom)
        if matches:
            results[category] = matches
    
    logger.info(f"Found {sum(len(matches) for matches in results.values())} matching symptoms")
    return results

def calculate_progress(patient_data: Dict[str, Any]) -> Dict[str, float]:
    """Calculate completion progress for different sections"""
    logger.info("Calculating patient data completion progress")
    
    progress = {}
    
    # Demographics progress
    demographics = patient_data.get('demographics', {})
    dem_fields = ['age', 'gender', 'occupation']
    progress['demographics'] = sum(1 for field in dem_fields if demographics.get(field)) / len(dem_fields)
    
    # Present illness progress
    present_illness = patient_data.get('present_illness', {})
    total_symptoms = sum(len(symptoms) for symptoms in SYMPTOM_DICTIONARY.values())
    present_symptoms = sum(1 for symptoms in present_illness.values() 
                         for value in symptoms.values() if value)
    progress['present_illness'] = present_symptoms / total_symptoms if total_symptoms > 0 else 0
    
    # Risk assessment progress
    risk_assessment = patient_data.get('risk_assessment', {})
    risk_fields = ['present', 'severity', 'details']
    suicidal_progress = sum(1 for field in risk_fields if risk_assessment.get('suicidal', {}).get(field)) / len(risk_fields)
    homicidal_progress = sum(1 for field in risk_fields if risk_assessment.get('homicidal', {}).get(field)) / len(risk_fields)
    progress['risk_assessment'] = (suicidal_progress + homicidal_progress) / 2
    
    # MSE progress
    mse = patient_data.get('mse', {})
    mse_fields = ['appearance', 'behavior', 'speech', 'mood', 'affect', 'thought_process', 
                  'thought_content', 'perception', 'cognition', 'insight', 'judgment', 'orientation']
    progress['mse'] = sum(1 for field in mse_fields if mse.get(field)) / len(mse_fields)
    
    # Overall progress
    progress['overall'] = sum(progress.values()) / len(progress)
    
    logger.info(f"Progress calculated - Overall: {progress['overall']:.1%}")
    return progress

def create_analytics_dashboard():
    """Create analytics dashboard with insights"""
    logger.info("Creating analytics dashboard")
    
    st.subheader("📊 Analytics Dashboard")
    
    if not st.session_state.patients:
        st.info("No patient data available for analysis")
        return
    
    # Load all patient data
    patients_data = []
    for patient_info in st.session_state.patients:
        patient_data = db_manager.load_patient(patient_info['patient_code'])
        if patient_data:
            patients_data.append(patient_data)
    
    if not patients_data:
        st.info("No patient data available for analysis")
        return
    
    logger.info(f"Analyzing data for {len(patients_data)} patients")
    
    # Create tabs for different analytics
    tab1, tab2, tab3, tab4 = st.tabs(["📈 Demographics", "🧠 Symptoms", "⚠️ Risk Analysis", "📋 Assessment Trends"])
    
    with tab1:
        st.subheader("Demographics Analysis")
        
        # Age distribution
        ages = []
        for patient in patients_data:
            age = patient['demographics'].get('age')
            if age and age.isdigit():
                ages.append(int(age))
        
        if ages:
            logger.info(f"Plotting age distribution for {len(ages)} patients")
            fig_age = px.histogram(x=ages, nbins=20, title="Age Distribution")
            fig_age.update_xaxes(title="Age")
            fig_age.update_yaxes(title="Count")
            st.plotly_chart(fig_age, use_container_width=True)
        
        # Gender distribution
        genders = [patient['demographics'].get('gender', 'Unknown') for patient in patients_data]
        gender_counts = pd.Series(genders).value_counts()
        
        logger.info(f"Gender distribution: {dict(gender_counts)}")
        
        fig_gender = px.pie(values=gender_counts.values, names=gender_counts.index, 
                           title="Gender Distribution")
        st.plotly_chart(fig_gender, use_container_width=True)
        
        # Additional demographic analysis
        # - Age by gender distribution
        # - Occupation categories
        # - Geographic distribution (if collected)
    
    with tab2:
        st.subheader("Symptom Analysis")
        
        # Symptom frequency
        symptom_counts = {}
        for patient in patients_data:
            for category, symptoms in patient['present_illness'].items():
                for symptom, present in symptoms.items():
                    if present:
                        symptom_counts[symptom] = symptom_counts.get(symptom, 0) + 1
        
        logger.info(f"Found {len(symptom_counts)} unique symptoms")
        
        if symptom_counts:
            # Top 20 symptoms
            top_symptoms = dict(sorted(symptom_counts.items(), key=lambda x: x[1], reverse=True)[:20])
            
            fig_symptoms = px.bar(x=list(top_symptoms.keys()), y=list(top_symptoms.values()),
                                 title="Top 20 Most Frequent Symptoms")
            fig_symptoms.update_xaxes(title="Symptom")
            fig_symptoms.update_yaxes(title="Frequency")
            fig_symptoms.update_layout(xaxis_tickangle=-45)
            st.plotly_chart(fig_symptoms, use_container_width=True)
            
            # Symptom co-occurrence analysis
            # Symptom category breakdown
            # Symptom severity analysis
    
    with tab3:
        st.subheader("Risk Assessment Analysis")
        
        # Suicidal ideation
        suicidal_count = sum(1 for patient in patients_data 
                           if patient['risk_assessment']['suicidal'].get('present', False))
        homicidal_count = sum(1 for patient in patients_data 
                            if patient['risk_assessment']['homicidal'].get('present', False))
        
        logger.info(f"Risk analysis - Suicidal: {suicidal_count}, Homicidal: {homicidal_count}")
        
        risk_data = pd.DataFrame({
            'Risk Type': ['Suicidal Ideation', 'Homicidal Ideation'],
            'Count': [suicidal_count, homicidal_count]
        })
        
        fig_risk = px.bar(risk_data, x='Risk Type', y='Count', title="Risk Assessment Summary")
        st.plotly_chart(fig_risk, use_container_width=True)
        
        # Risk severity distribution
        suicidal_severity = [patient['risk_assessment']['suicidal'].get('severity', 'Unknown') 
                           for patient in patients_data 
                           if patient['risk_assessment']['suicidal'].get('present', False)]
        
        if suicidal_severity:
            severity_counts = pd.Series(suicidal_severity).value_counts()
            fig_severity = px.pie(values=severity_counts.values, names=severity_counts.index,
                                 title="Suicidal Ideation Severity Distribution")
            st.plotly_chart(fig_severity, use_container_width=True)
            
        # Risk factor correlation analysis
        # Protective factor analysis
    
    with tab4:
        st.subheader("Assessment Trends")
        
        # Assessments over time
        dates = [datetime.fromisoformat(patient.get('last_modified', datetime.now().isoformat())) 
                for patient in patients_data]
        
        if dates:
            logger.info(f"Analyzing assessment trends for {len(dates)} assessments")
            # Group by month
            date_df = pd.DataFrame({'date': dates})
            date_df['month'] = date_df['date'].dt.to_period('M')
            monthly_counts = date_df.groupby('month').size()
            
            fig_trends = px.line(x=monthly_counts.index.astype(str), y=monthly_counts.values,
                               title="Assessments Over Time")
            fig_trends.update_xaxes(title="Month")
            fig_trends.update_yaxes(title="Number of Assessments")
            st.plotly_chart(fig_trends, use_container_width=True)
            
            # Seasonal pattern analysis
            # Day-of-week patterns
            # Assessment completion time analysis

def render_rating_scales():
    """Render standardized rating scales"""
    logger.info("Rendering rating scales")
    
    st.subheader("📋 Standardized Rating Scales")
    
    # Add scale selection based on patient symptoms
    # Add automatic scoring and interpretation
    
    # PHQ-9
    with st.expander("PHQ-9 (Depression)", expanded=False):
        st.write("Over the last 2 weeks, how often have you been bothered by the following problems?")
        
        phq9_scores = {}
        for question in RatingScales.PHQ9_QUESTIONS:
            score = st.selectbox(
                question,
                options=[0, 1, 2, 3],
                format_func=lambda x: ["Not at all", "Several days", "More than half the days", "Nearly every day"][x],
                key=f"phq9_{question}"
            )
            phq9_scores[question] = score
        
        # Calculate total score
        total_score = sum(phq9_scores.values())
        st.metric("PHQ-9 Total Score", total_score)
        
        logger.info(f"PHQ-9 total score: {total_score}")
        
        # Interpretation
        if total_score >= 20:
            st.error("Severe depression")
        elif total_score >= 15:
            st.warning("Moderately severe depression")
        elif total_score >= 10:
            st.info("Moderate depression")
        elif total_score >= 5:
            st.success("Mild depression")
        else:
            st.success("Minimal depression")
        
        # Add suicide risk calculation from question 9
        suicide_risk_score = phq9_scores.get("Thoughts that you would be better off dead, or of hurting yourself", 0)
        if suicide_risk_score > 0:
            st.warning(f"⚠️ Suicide risk indicated (Score: {suicide_risk_score})")
        
        # Save to patient data
        if st.button("Save PHQ-9 Scores"):
            st.session_state.current_patient['rating_scales']['phq9'] = phq9_scores
            st.success("PHQ-9 scores saved!")
            logger.info("PHQ-9 scores saved to patient data")
    
    # GAD-7
    with st.expander("GAD-7 (Anxiety)", expanded=False):
        st.write("Over the last 2 weeks, how often have you been bothered by the following problems?")
        
        gad7_scores = {}
        for question in RatingScales.GAD7_QUESTIONS:
            score = st.selectbox(
                question,
                options=[0, 1, 2, 3],
                format_func=lambda x: ["Not at all", "Several days", "More than half the days", "Nearly every day"][x],
                key=f"gad7_{question}"
            )
            gad7_scores[question] = score
        
        # Calculate total score
        total_score = sum(gad7_scores.values())
        st.metric("GAD-7 Total Score", total_score)
        
        logger.info(f"GAD-7 total score: {total_score}")
        
        # Interpretation
        if total_score >= 15:
            st.error("Severe anxiety")
        elif total_score >= 10:
            st.warning("Moderate anxiety")
        elif total_score >= 5:
            st.info("Mild anxiety")
        else:
            st.success("Minimal anxiety")
        
        # Save to patient data
        if st.button("Save GAD-7 Scores"):
            st.session_state.current_patient['rating_scales']['gad7'] = gad7_scores
            st.success("GAD-7 scores saved!")
            logger.info("GAD-7 scores saved to patient data")
    
    # BPRS
    with st.expander("BPRS (Psychosis)", expanded=False):
        st.write("Please rate the following symptoms on a scale of 1-7:")
        st.write("1=Not present, 2=Very mild, 3=Mild, 4=Moderate, 5=Moderately severe, 6=Severe, 7=Extremely severe")
        
        # Remove duplicate "Conceptual disorganization" entry
        unique_bprs_questions = list(dict.fromkeys(RatingScales.BPRS_QUESTIONS))  # Remove duplicates
        
        bprs_scores = {}
        for question in unique_bprs_questions:
            score = st.slider(question, 1, 7, 1, key=f"bprs_{question}")
            bprs_scores[question] = score
        
        # Calculate total score
        total_score = sum(bprs_scores.values())
        st.metric("BPRS Total Score", total_score)
        
        logger.info(f"BPRS total score: {total_score}")
        
        # Interpretation
        if total_score >= 52:
            st.error("Severe psychosis")
        elif total_score >= 36:
            st.warning("Moderate psychosis")
        elif total_score >= 24:
            st.info("Mild psychosis")
        else:
            st.success("Minimal psychosis")
        
        # Save to patient data
        if st.button("Save BPRS Scores"):
            st.session_state.current_patient['rating_scales']['bprs'] = bprs_scores
            st.success("BPRS scores saved!")
            logger.info("BPRS scores saved to patient data")
    
    # MMSE
    with st.expander("MMSE (Cognitive Screening)", expanded=False):
        st.write("Mini-Mental State Examination")
        
        mmse_scores = {}
        total_possible = 30
        
        # Orientation (10 points)
        st.subheader("Orientation (10 points)")
        orientation_score = 0
        year = st.number_input("What year is it?", 1900, 2100, 2023, key="mmse_year")
        if year == datetime.now().year:
            orientation_score += 1
        
        season = st.selectbox("What season is it?", ["Spring", "Summer", "Fall", "Winter"], key="mmse_season")
        if season in get_current_season():
            orientation_score += 1
        
        date = st.date_input("What is today's date?", key="mmse_date")
        if date == date.today():
            orientation_score += 1
        
        month = st.selectbox("What month is it?", list(range(1, 13)), key="mmse_month")
        if month == datetime.now().month:
            orientation_score += 1
        
        day_of_week = st.selectbox("What day of the week is it?", ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], key="mmse_dow")
        if day_of_week == datetime.now().strftime("%A"):
            orientation_score += 1
        
        country = st.text_input("What country are we in?", key="mmse_country")
        if country.lower() in ["united states", "usa", "america"]:
            orientation_score += 1
        
        state = st.text_input("What state are we in?", key="mmse_state")
        # Could add validation for state
        
        city = st.text_input("What city are we in?", key="mmse_city")
        # Could add validation for city
        
        floor = st.number_input("What floor are we on?", 0, 100, 1, key="mmse_floor")
        
        hospital = st.text_input("What hospital are we in?", key="mmse_hospital")
        
        mmse_scores['Orientation'] = orientation_score
        
        # Registration (3 points)
        st.subheader("Registration (3 points)")
        st.write("I will name three objects and I want you to repeat them back to me.")
        objects = ["apple", "table", "penny"]
        st.write(f"Remember these: {', '.join(objects)}")
        
        reg_recall1 = st.text_input("First object:", key="mmse_reg1")
        reg_recall2 = st.text_input("Second object:", key="mmse_reg2")
        reg_recall3 = st.text_input("Third object:", key="mmse_reg3")
        
        reg_score = sum(1 for obj, recall in zip(objects, [reg_recall1, reg_recall2, reg_recall3]) 
                      if recall.lower() == obj.lower())
        mmse_scores['Registration'] = reg_score
        
        # Attention and Calculation (5 points)
        st.subheader("Attention and Calculation (5 points)")
        st.write("Now I want you to count backward from 100 by 7s.")
        st.write("100, 93, 86, 79, 72, 65...")
        
        calc_attempts = st.text_area("Enter the numbers you said:", key="mmse_calc")
        calc_numbers = [n.strip() for n in calc_attempts.split() if n.strip()]
        correct_sequence = ["100", "93", "86", "79", "72"]
        calc_score = sum(1 for i, num in enumerate(calc_numbers[:5]) 
                        if i < len(correct_sequence) and num == correct_sequence[i])
        mmse_scores['Attention'] = calc_score
        
        # Recall (3 points)
        st.subheader("Recall (3 points)")
        st.write("What were the three objects I asked you to remember?")
        
        recall1 = st.text_input("First object:", key="mmse_recall1")
        recall2 = st.text_input("Second object:", key="mmse_recall2")
        recall3 = st.text_input("Third object:", key="mmse_recall3")
        
        recall_score = sum(1 for obj, recall in zip(objects, [recall1, recall2, recall3]) 
                         if recall.lower() == obj.lower())
        mmse_scores['Recall'] = recall_score
        
        # Language (9 points)
        st.subheader("Language (9 points)")
        lang_score = 0
        
        # Naming
        watch = st.text_input("What is this called?", key="mmse_watch")
        if watch.lower() in ["watch"]:
            lang_score += 1
        
        pencil = st.text_input("What is this called?", key="mmse_pencil")
        if pencil.lower() in ["pencil"]:
            lang_score += 1
        
        # Repetition
        repetition = st.text_input("Repeat after me: 'No ifs, ands, or buts'", key="mmse_repetition")
        if "no ifs ands or buts" in repetition.lower():
            lang_score += 1
        
        # 3-stage command
        st.write("Take this paper in your right hand, fold it in half, and put it on the floor.")
        # Would need visual confirmation in real setting
        command_score = st.slider("How well did they follow the 3-stage command? (0-3)", 0, 3, 0, key="mmse_command")
        lang_score += command_score
        
        # Reading
        reading = st.text_input("Read and obey this: 'CLOSE YOUR EYES'", key="mmse_reading")
        if "close your eyes" in reading.lower():
            lang_score += 1
        
        # Writing
        st.write("Write a complete sentence.")
        sentence = st.text_area("Sentence:", key="mmse_sentence")
        if len(sentence.split()) > 2 and any(char.isalpha() for char in sentence):
            lang_score += 1
        
        # Copying
        st.write("Copy this drawing:")
        # Would need visual assessment in real setting
        copy_score = st.slider("How well did they copy the drawing? (0-1)", 0, 1, 0, key="mmse_copy")
        lang_score += copy_score
        
        mmse_scores['Language'] = lang_score
        
        # Calculate total score
        total_mmse = sum(mmse_scores.values())
        st.metric("MMSE Total Score", f"{total_mmse}/{total_possible}")
        
        # Interpretation
        if total_mmse >= 27:
            st.success("Normal cognition")
        elif total_mmse >= 21:
            st.warning("Mild cognitive impairment")
        elif total_mmse >= 11:
            st.error("Moderate cognitive impairment")
        else:
            st.error("Severe cognitive impairment")
        
        # Save to patient data
        if st.button("Save MMSE Scores"):
            st.session_state.current_patient['rating_scales']['mmse'] = mmse_scores
            st.success("MMSE scores saved!")
            logger.info("MMSE scores saved to patient data")

def get_current_season():
    """Get current season"""
    month = datetime.now().month
    if month in [12, 1, 2]:
        return ["Winter"]
    elif month in [3, 4, 5]:
        return ["Spring"]
    elif month in [6, 7, 8]:
        return ["Summer"]
    else:
        return ["Fall"]

def render_progress_tracking():
    """Render progress tracking and completion indicators"""
    logger.info("Rendering progress tracking")
    
    st.subheader("📊 Progress Tracking")
    
    # Calculate progress
    progress = calculate_progress(st.session_state.current_patient)
    
    # Display progress bars
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Demographics", f"{progress['demographics']:.1%}")
        st.progress(progress['demographics'])
    
    with col2:
        st.metric("Present Illness", f"{progress['present_illness']:.1%}")
        st.progress(progress['present_illness'])
    
    with col3:
        st.metric("Risk Assessment", f"{progress['risk_assessment']:.1%}")
        st.progress(progress['risk_assessment'])
    
    with col4:
        st.metric("MSE", f"{progress['mse']:.1%}")
        st.progress(progress['mse'])
    
    # Overall progress
    st.subheader("Overall Completion")
    st.metric("Total Progress", f"{progress['overall']:.1%}")
    st.progress(progress['overall'])
    
    # Color-code progress levels
    if progress['overall'] < 0.3:
        st.error("⚠️ Assessment significantly incomplete")
    elif progress['overall'] < 0.7:
        st.warning("📝 Assessment partially complete")
    else:
        st.success("✅ Assessment substantially complete")
    
    # Required fields indicator
    st.subheader("Required Fields")
    required_fields = [
        ("Patient Code", st.session_state.current_patient['patient_code']),
        ("Age", st.session_state.current_patient['demographics']['age']),
        ("Gender", st.session_state.current_patient['demographics']['gender'])
    ]
    
    missing_required = 0
    for field_name, field_value in required_fields:
        if field_value:
            st.success(f"✅ {field_name}")
        else:
            st.warning(f"⚠️ {field_name} - Required")
            missing_required += 1
    
    if missing_required > 0:
        st.error(f"❌ {missing_required} required fields missing")
    
    # Estimated completion time
    # Section-specific recommendations

def render_symptom_section(title, category, symptoms):
    """Render a collapsible symptom section"""
    logger.info(f"Rendering symptom section: {title}")
    
    with st.expander(f"🧠 {title}", expanded=False):
        # Symptom search within section
        # Bulk selection options
        # Severity levels for each symptom
        
        # Quick symptom checkboxes
        cols = st.columns(3)
        changes_made = False
        
        for i, (symptom, code) in enumerate(symptoms.items()):
            col = cols[i % 3]
            with col:
                current_value = st.session_state.current_patient['present_illness'][category].get(symptom, False)
                new_value = st.checkbox(f"{symptom} ({code})", value=current_value, key=f"{category}_{symptom}")
                if new_value != current_value:
                    st.session_state.current_patient['present_illness'][category][symptom] = new_value
                    changes_made = True
        
        if changes_made:
            logger.info(f"Changes made to {title} symptoms")
        
        # Symptom timeline/duration input
        # Symptom severity rating
        # Associated functional impairment

def render_patient_summary():
    """Render patient summary dashboard"""
    logger.info("Rendering patient summary")
    
    patient = st.session_state.current_patient
    
    st.subheader(f"👤 Patient Summary: {patient['patient_code'] or 'New Patient'}")
    
    # Add patient photo/avatar support
    # Add patient contact information
    
    # Demographics
    with st.expander("📋 Demographics", expanded=True):
        col1, col2, col3 = st.columns(3)
        with col1:
            age = st.text_input("Age", patient['demographics']['age'], key="age")
            patient['demographics']['age'] = age
        with col2:
            gender = st.selectbox("Gender", ["", "Male", "Female", "Other"], 
                                index=["", "Male", "Female", "Other"].index(patient['demographics']['gender']) 
                                if patient['demographics']['gender'] in ["", "Male", "Female", "Other"] else 0,
                                key="gender")
            patient['demographics']['gender'] = gender
        with col3:
            occupation = st.text_input("Occupation", patient['demographics']['occupation'], key="occupation")
            patient['demographics']['occupation'] = occupation
        
        # Additional demographic fields
        # - Marital status, education level, insurance, emergency contact
    
    # Progress tracking
    render_progress_tracking()
    
    # Risk assessment summary
    risk_assessment = patient['risk_assessment']
    if risk_assessment['suicidal']['present'] or risk_assessment['homicidal']['present']:
        st.error("⚠️ RISK ASSESSMENT ALERT")
        if risk_assessment['suicidal']['present']:
            severity = risk_assessment['suicidal']['severity']
            st.error(f"🔴 Suicidal ideation present - Severity: {severity}")
            
            # Add risk factor calculation
            risk_factors = []
            if risk_assessment['suicidal']['plan']:
                risk_factors.append("Has plan")
            if risk_assessment['suicidal']['means']:
                risk_factors.append("Has means")
            if risk_assessment['suicidal']['intent']:
                risk_factors.append("Has intent")
            
            if risk_factors:
                st.error(f"Additional risk factors: {', '.join(risk_factors)}")
                
        if risk_assessment['homicidal']['present']:
            severity = risk_assessment['homicidal']['severity']
            st.error(f"🔴 Homicidal ideation present - Severity: {severity}")
    
    # Quick action buttons
    # - Schedule follow-up, Print summary, Send to colleague

# New utility functions for ML and analysis
def calculate_ml_features(patient_data: Dict[str, Any]) -> Dict[str, Any]:
    """Calculate derived features for ML training"""
    features = {}
    
    # Calculate symptom cluster scores
    mood_symptoms = sum(patient_data['present_illness'].get('mood', {}).values())
    anxiety_symptoms = sum(patient_data['present_illness'].get('anxiety', {}).values())
    psychotic_symptoms = sum(patient_data['present_illness'].get('psychotic', {}).values())
    
    features['mood_symptom_count'] = mood_symptoms
    features['anxiety_symptom_count'] = anxiety_symptoms
    features['psychotic_symptom_count'] = psychotic_symptoms
    features['total_symptom_count'] = mood_symptoms + anxiety_symptoms + psychotic_symptoms
    
    # Risk score calculation
    risk_score = 0
    if patient_data['risk_assessment']['suicidal']['present']:
        severity = patient_data['risk_assessment']['suicidal']['severity'].lower()
        if severity == 'low':
            risk_score = 1
        elif severity == 'moderate':
            risk_score = 2
        elif severity == 'high':
            risk_score = 3
        elif severity == 'imminent':
            risk_score = 4
    
    features['calculated_risk_score'] = risk_score
    
    # Medication burden
    current_meds = len(patient_data['medication_history']['current_medications'])
    features['medication_count'] = current_meds
    
    # Comorbidity count
    comorbidity_count = sum(patient_data['medical_history']['conditions'].values())
    features['comorbidity_count'] = comorbidity_count
    
    # Hospitalization frequency
    psych_history = patient_data.get('psychiatric_history', {})
    total_hosp = psych_history.get('total_hospitalizations', '')
    if total_hosp and total_hosp.isdigit():
        features['hospitalization_count'] = int(total_hosp)
    else:
        features['hospitalization_count'] = 0
    
    return features

def calculate_days_since_hospitalization(patient_data: Dict[str, Any]) -> int:
    """Calculate days since last hospitalization"""
    try:
        last_hosp = patient_data.get('psychiatric_history', {}).get('last_hospitalization', '')
        if last_hosp:
            last_date = datetime.strptime(last_hosp, '%Y-%m-%d').date()
            return (date.today() - last_date).days
    except:
        pass
    return -1  # Indicates no hospitalization or invalid date

def add_derived_features(df: pd.DataFrame) -> pd.DataFrame:
    """Add derived features for ML"""
    # Symptom ratios
    if 'mood_symptom_count' in df.columns and 'total_symptom_count' in df.columns:
        df['mood_symptom_ratio'] = df['mood_symptom_count'] / (df['total_symptom_count'] + 1)
    
    if 'anxiety_symptom_count' in df.columns and 'total_symptom_count' in df.columns:
        df['anxiety_symptom_ratio'] = df['anxiety_symptom_count'] / (df['total_symptom_count'] + 1)
    
    # Risk-medication interaction
    if 'calculated_risk_score' in df.columns and 'medication_count' in df.columns:
        df['risk_medication_interaction'] = df['calculated_risk_score'] * df['medication_count']
    
    # Age groups
    if 'age' in df.columns:
        df['age_group'] = pd.cut(df['age'], bins=[0, 18, 35, 50, 65, 120], 
                                labels=['pediatric', 'young_adult', 'adult', 'middle_aged', 'geriatric'])
    
    return df

def remove_low_variance_features(df: pd.DataFrame, threshold: float = 0.01) -> pd.DataFrame:
    """Remove features with low variance"""
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    cols_to_keep = []
    
    for col in numeric_cols:
        if df[col].var() > threshold:
            cols_to_keep.append(col)
    
    # Keep all non-numeric columns
    non_numeric_cols = df.select_dtypes(exclude=[np.number]).columns
    cols_to_keep.extend(non_numeric_cols)
    
    return df[cols_to_keep]

def validate_lab_values(lab_data: Dict[str, Any]) -> List[str]:
    """Validate laboratory values against reference ranges"""
    warnings = []
    
    # Add comprehensive reference ranges
    reference_ranges = {
        'cbc': {
            'WBC': (4.0, 11.0),
            'RBC': (4.2, 5.4),
            'Hgb': (12.0, 16.0),
            'Platelets': (150, 450)
        },
        'lft': {
            'ALT': (7, 56),
            'AST': (10, 40)
        },
        'electrolytes': {
            'Sodium': (135, 145),
            'Potassium': (3.5, 5.0),
            'Glucose': (70, 100)
        }
        # More reference ranges
    }
    
    for category, tests in lab_data.items():
        if category in reference_ranges:
            for test, value in tests.items():
                if test in reference_ranges[category] and value:
                    try:
                        numeric_value = float(value)
                        min_val, max_val = reference_ranges[category][test]
                        if numeric_value < min_val or numeric_value > max_val:
                            warnings.append(f"{test} value ({numeric_value}) outside reference range ({min_val}-{max_val})")
                    except ValueError:
                        continue
    
    return warnings

# Main application
def main():
    """Main application function"""
    logger.info("Starting main application")
    
    initialize_session_state()
    
    # Add user authentication
    # Add application logging
    # Add error recovery mechanisms
    
    # Header
    st.title("🧠 Enhanced Psychiatric Assessment System")
    st.markdown("*Comprehensive psychiatric assessment with ML-ready data export*")
    
    # Debug mode toggle
    if st.sidebar.checkbox("🔧 Debug Mode"):
        st.sidebar.write(f"Session ID: {st.session_state.session_id}")
        st.sidebar.write(f"Total Patients: {len(st.session_state.patients)}")
        st.sidebar.write(f"Current Patient: {st.session_state.current_patient.get('patient_code', 'None')}")
        
        # Session state inspector
        if st.sidebar.button("Show Session State"):
            st.sidebar.json(st.session_state.current_patient)
    
    # Sidebar
    with st.sidebar:
        st.header("🔧 Controls")
        
        # Patient selection
        st.subheader("👥 Patient Management")
        patient_options = ["New Patient"] + [p['patient_code'] for p in st.session_state.patients]
        selected_patient = st.selectbox("Select Patient", patient_options)
        
        if selected_patient != "New Patient":
            if selected_patient != st.session_state.current_patient.get('patient_code'):
                load_patient(selected_patient)
        else:
            if st.session_state.current_patient.get('patient_code'):
                st.session_state.current_patient = create_empty_patient()
                st.rerun()
        
        # Patient code input for new patients
        if selected_patient == "New Patient":
            new_patient_code = st.text_input("Patient Code", 
                                            value=st.session_state.current_patient['patient_code'],
                                            placeholder="Enter unique patient code")
            if new_patient_code != st.session_state.current_patient['patient_code']:
                st.session_state.current_patient['patient_code'] = new_patient_code
        
        # Save/Load/Delete buttons
        col1, col2 = st.columns(2)
        with col1:
            if st.button("💾 Save", type="primary"):
                save_patient()
        with col2:
            if st.button("🗑️ Delete", type="secondary"):
                if st.session_state.current_patient['patient_code']:
                    # Confirmation dialog
                    if st.session_state.get('confirm_delete', False):
                        if db_manager.delete_patient(st.session_state.current_patient['patient_code']):
                            st.success("Patient deleted!")
                            st.session_state.patients = db_manager.get_all_patients()
                            st.session_state.current_patient = create_empty_patient()
                            st.session_state.confirm_delete = False
                            st.rerun()
                    else:
                        st.session_state.confirm_delete = True
                        st.warning("Click again to confirm deletion")
        
        # Assessment templates
        st.subheader("📋 Assessment Templates")
        template_name = st.selectbox("Choose Template", [""] + list(ASSESSMENT_TEMPLATES.keys()))
        if template_name and st.button("Apply Template"):
            logger.info(f"Applying template: {template_name}")
            template = ASSESSMENT_TEMPLATES[template_name]
            # Apply template to current patient
            for section, data in template.items():
                if section in st.session_state.current_patient:
                    if isinstance(st.session_state.current_patient[section], dict):
                        for key, value in data.items():
                            if isinstance(value, dict):
                                st.session_state.current_patient[section][key].update(value)
                            else:
                                st.session_state.current_patient[section][key] = value
                    else:
                        st.session_state.current_patient[section] = data
            st.success(f"Applied {template_name} template!")
            logger.info(f"Template {template_name} applied successfully")
        
        # Export options
        st.subheader("📤 Export Data")
        export_format = st.selectbox("Format", ["CSV", "JSON", "Excel"])
        ml_ready = st.checkbox("ML-Ready (preprocessed)")
        if st.button("Export"):
            export_data(export_format.lower(), ml_ready)
        
        # Analytics dashboard
        if st.button("📊 Analytics Dashboard"):
            create_analytics_dashboard()
        
        # Data import
        st.subheader("📥 Import Data")
        uploaded_file = st.file_uploader("Upload JSON data", type=['json'])
        if uploaded_file is not None:
            try:
                logger.info("Processing uploaded file")
                data = json.load(uploaded_file)
                if isinstance(data, list):
                    success_count = 0
                    for patient_data in data:
                        if db_manager.save_patient(patient_data):
                            success_count += 1
                    st.session_state.patients = db_manager.get_all_patients()
                    st.success(f"Imported {success_count}/{len(data)} patients!")
                    logger.info(f"Imported {success_count}/{len(data)} patients")
                else:
                    st.error("Invalid JSON format. Expected list of patients.")
            except json.JSONDecodeError as e:
                st.error(f"Invalid JSON file: {str(e)}")
                logger.error(f"JSON decode error: {e}")
            except Exception as e:
                st.error(f"Error importing data: {str(e)}")
                logger.error(f"Import error: {e}")
        
        # Backup and restore functionality
        st.subheader("💾 Backup & Restore")
        if st.button("Create Backup"):
            backup_data = {
                'patients': st.session_state.patients,
                'timestamp': datetime.now().isoformat(),
                'version': '1.0'
            }
            backup_json = json.dumps(backup_data, indent=2)
            st.download_button(
                label="Download Backup",
                data=backup_json,
                file_name=f"psychiatric_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )
            logger.info("Backup created")
        
        # Data validation report
        st.subheader("📋 Data Quality Report")
        if st.button("Generate Report"):
            quality_metrics = assess_data_quality()
            st.json(quality_metrics)
        
        # System status indicators
        st.subheader("🔧 System Status")
        st.write(f"Database: {len(st.session_state.patients)} patients")
        st.write(f"Session: {st.session_state.session_id[:8]}...")
        st.write(f"Python: {pd.__version__}")
        st.write(f"SQLite: {sqlite3.sqlite_version}")
    
    # Main content area
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
        "📋 Patient Summary", "🧠 Present Illness", "⚠️ Risk Assessment", 
        "📚 History", "💊 Medications", "🔬 MSE & Labs"
    ])
    
    with tab1:
        render_patient_summary()
    
    with tab2:
        st.subheader("🧠 Present Illness")
        
        # Symptom search
        search_term = st.text_input("🔍 Search symptoms...")
        if search_term:
            search_results = search_symptoms(search_term)
            if search_results:
                st.write("Search results:")
                for category, symptoms in search_results.items():
                    st.write(f"**{category.title()}**: {', '.join(symptoms)}")
            else:
                st.info("No symptoms found matching your search.")
        
        # Quick assessment buttons
        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("🚀 Quick Depression Screen"):
                # Auto-select common depression symptoms
                depression_symptoms = ['Depressed mood', 'Anhedonia', 'Fatigue', 'Concentration problems']
                for symptom in depression_symptoms:
                    st.session_state.current_patient['present_illness']['mood'][symptom] = True
                st.success("Depression symptoms selected")
        
        with col2:
            if st.button("⚡ Quick Anxiety Screen"):
                # Auto-select common anxiety symptoms
                anxiety_symptoms = ['Excessive worry', 'Restlessness', 'Muscle tension']
                for symptom in anxiety_symptoms:
                    st.session_state.current_patient['present_illness']['anxiety'][symptom] = True
                st.success("Anxiety symptoms selected")
        
        with col3:
            if st.button("🧠 Quick Psychosis Screen"):
                # Auto-select common psychotic symptoms
                psychotic_symptoms = ['Delusions', 'Auditory hallucinations']
                for symptom in psychotic_symptoms:
                    st.session_state.current_patient['present_illness']['psychotic'][symptom] = True
                st.success("Psychotic symptoms selected")
        
        # Symptom sections
        render_symptom_section("Mood Symptoms", "mood", SYMPTOM_DICTIONARY['mood'])
        render_symptom_section("Bipolar Symptoms", "bipolar", SYMPTOM_DICTIONARY['bipolar'])
        render_symptom_section("Anxiety Symptoms", "anxiety", SYMPTOM_DICTIONARY['anxiety'])
        render_symptom_section("Psychotic Symptoms", "psychotic", SYMPTOM_DICTIONARY['psychotic'])
        render_symptom_section("Cognitive Symptoms", "cognitive", SYMPTOM_DICTIONARY['cognitive'])
        render_symptom_section("Somatic Symptoms", "somatic", SYMPTOM_DICTIONARY['somatic'])
        render_symptom_section("Behavioral Symptoms", "behavioral", SYMPTOM_DICTIONARY['behavioral'])
        
        # Rating scales
        render_rating_scales()
    
    with tab3:
        st.subheader("⚠️ Risk Assessment")
        
        # Add risk calculation algorithms
        # Add protective factors assessment
        
        # Suicidal risk
        with st.expander("🚨 Suicidal Risk Assessment", expanded=True):
            suicidal = st.session_state.current_patient['risk_assessment']['suicidal']
            
            # Primary risk question
            suicidal['present'] = st.checkbox("Suicidal ideation present", value=suicidal['present'])
            
            if suicidal['present']:
                st.warning("⚠️ Suicidal ideation detected - Complete full assessment")
                
                col1, col2 = st.columns(2)
                with col1:
                    st.subheader("Risk Factors")
                    suicidal['ideation'] = st.checkbox("Active suicidal ideation", value=suicidal['ideation'])
                    suicidal['plan'] = st.checkbox("Suicidal plan", value=suicidal['plan'])
                    suicidal['intent'] = st.checkbox("Suicidal intent", value=suicidal['intent'])
                    suicidal['means'] = st.checkbox("Means available", value=suicidal['means'])
                    suicidal['previous_attempts'] = st.checkbox("Previous attempts", value=suicidal['previous_attempts'])
                    suicidal['impulsivity'] = st.checkbox("Impulsivity", value=suicidal['impulsivity'])
                
                with col2:
                    st.subheader("Additional Factors")
                    suicidal['hopelessness'] = st.checkbox("Hopelessness", value=suicidal['hopelessness'])
                    suicidal['social_isolation'] = st.checkbox("Social isolation", value=suicidal['social_isolation'])
                    suicidal['substance_use'] = st.checkbox("Substance use", value=suicidal['substance_use'])
                    suicidal['psychosis'] = st.checkbox("Psychosis", value=suicidal['psychosis'])
                    suicidal['command_hallucinations'] = st.checkbox("Command hallucinations", value=suicidal['command_hallucinations'])
                    suicidal['poor_insight'] = st.checkbox("Poor insight", value=suicidal['poor_insight'])
                
                # Severity assessment
                suicidal['severity'] = st.selectbox("Overall Risk Severity", 
                    ["", "Low", "Moderate", "High", "Imminent"],
                    index=["", "Low", "Moderate", "High", "Imminent"].index(suicidal['severity']) 
                    if suicidal['severity'] in ["", "Low", "Moderate", "High", "Imminent"] else 0
                )
                
                # Risk score calculation
                risk_factors_count = sum([
                    suicidal['plan'], suicidal['intent'], suicidal['means'], 
                    suicidal['previous_attempts'], suicidal['hopelessness'],
                    suicidal['social_isolation'], suicidal['substance_use']
                ])
                
                st.metric("Risk Factors Present", risk_factors_count)
                
                if risk_factors_count >= 5:
                    st.error("🔴 HIGH RISK - Multiple risk factors present")
                elif risk_factors_count >= 3:
                    st.warning("🟡 MODERATE RISK - Several risk factors present")
                else:
                    st.info("🟢 LOWER RISK - Few risk factors present")
            
            suicidal['details'] = st.text_area("Risk Assessment Details", value=suicidal['details'])
        
        # Homicidal risk
        with st.expander("⚠️ Homicidal Risk Assessment", expanded=True):
            homicidal = st.session_state.current_patient['risk_assessment']['homicidal']
            
            homicidal['present'] = st.checkbox("Homicidal ideation present", value=homicidal['present'])
            if homicidal['present']:
                st.warning("⚠️ Homicidal ideation detected")
                homicidal['severity'] = st.selectbox("Severity", 
                    ["", "Low", "Moderate", "High", "Imminent"],
                    index=["", "Low", "Moderate", "High", "Imminent"].index(homicidal['severity']) 
                    if homicidal['severity'] in ["", "Low", "Moderate", "High", "Imminent"] else 0,
                    key="homicidal_severity"
                )
                homicidal['details'] = st.text_area("Homicidal Risk Details", value=homicidal['details'])
        
        # Protective factors assessment
        # Safety planning
        # Risk monitoring recommendations
    
    with tab4:
        st.subheader("📚 History")
        
        # Psychiatric history
        with st.expander("🏥 Psychiatric History", expanded=True):
            psych_hist = st.session_state.current_patient['psychiatric_history']
            
            col1, col2 = st.columns(2)
            with col1:
                psych_hist['total_hospitalizations'] = st.text_input("Total hospitalizations", value=psych_hist['total_hospitalizations'])
                psych_hist['first_hospitalization'] = st.text_input("First hospitalization (YYYY-MM-DD)", value=psych_hist['first_hospitalization'])
            with col2:
                psych_hist['last_hospitalization'] = st.text_input("Last hospitalization (YYYY-MM-DD)", value=psych_hist['last_hospitalization'])
                psych_hist['involuntary_admissions'] = st.text_input("Involuntary admissions", value=psych_hist['involuntary_admissions'])
            
            # Validate hospitalization dates
            if psych_hist['first_hospitalization'] and psych_hist['last_hospitalization']:
                try:
                    first_date = datetime.strptime(psych_hist['first_hospitalization'], '%Y-%m-%d')
                    last_date = datetime.strptime(psych_hist['last_hospitalization'], '%Y-%m-%d')
                    if last_date < first_date:
                        st.error("❌ Last hospitalization cannot be before first hospitalization")
                except ValueError:
                    st.error("❌ Invalid date format")
            
            # Suicide attempts
            with st.expander("🚨 Suicide Attempts"):
                suicide_attempts = psych_hist['suicide_attempts']
                suicide_attempts['history'] = st.checkbox("History of suicide attempts", value=suicide_attempts['history'])
                if suicide_attempts['history']:
                    col1, col2 = st.columns(2)
                    with col1:
                        suicide_attempts['number'] = st.text_input("Number of attempts", value=suicide_attempts['number'])
                    with col2:
                        suicide_attempts['most_recent'] = st.text_input("Most recent attempt (YYYY-MM-DD)", value=suicide_attempts['most_recent'])
                    
                    # Validate attempt data
                    if suicide_attempts['number']:
                        try:
                            num_attempts = int(suicide_attempts['number'])
                            if num_attempts <= 0:
                                st.error("Number of attempts must be positive")
                        except ValueError:
                            st.error("Number of attempts must be a valid number")
        
        # Substance history
        with st.expander("🍷 Substance History", expanded=True):
            substance_hist = st.session_state.current_patient['substance_history']
            
            # Substance use severity assessment
            # AUDIT/DAST screening tools
            
            for substance, data in substance_hist.items():
                with st.expander(f"{substance.title()}"):
                    data['use'] = st.checkbox("Current use", value=data['use'], key=f"substance_{substance}_use")
                    if data['use']:
                        col1, col2 = st.columns(2)
                        with col1:
                            data['frequency'] = st.selectbox("Frequency", 
                                ["Daily", "Weekly", "Monthly", "Occasional", "Unknown"],
                                index=0 if not data['frequency'] else 
                                ["Daily", "Weekly", "Monthly", "Occasional", "Unknown"].index(data['frequency']) 
                                if data['frequency'] in ["Daily", "Weekly", "Monthly", "Occasional", "Unknown"] else 0,
                                key=f"substance_{substance}_freq")
                        with col2:
                            data['amount'] = st.text_input("Amount/Day", value=data['amount'], key=f"substance_{substance}_amount")
                        data['problems'] = st.text_area("Related problems", value=data['problems'], key=f"substance_{substance}_problems")
                        
                        # Last use date
                        # Route of administration
                        # Withdrawal symptoms
        
        # Family history
        with st.expander("👨‍👩‍👧‍👦 Family History", expanded=False):
            st.write("Family psychiatric history")
            
            family_history = st.session_state.current_patient.get('family_history', {})
            
            # First-degree relatives
            st.subheader("First-Degree Relatives")
            family_members = ['Mother', 'Father', 'Siblings', 'Children']
            
            for member in family_members:
                with st.expander(f"{member}"):
                    has_psych_history = st.checkbox(f"Psychiatric history", 
                                                   value=family_history.get(f'{member.lower()}_psych_history', False),
                                                   key=f"family_{member}_psych")
                    if has_psych_history:
                        conditions = st.text_input("Conditions (comma-separated)", 
                                                 value=family_history.get(f'{member.lower()}_conditions', ''),
                                                 key=f"family_{member}_conditions")
                        family_history[f'{member.lower()}_conditions'] = conditions
                    
                    has_substance_use = st.checkbox(f"Substance use history", 
                                                   value=family_history.get(f'{member.lower()}_substance_use', False),
                                                   key=f"family_{member}_substance")
                    if has_substance_use:
                        substances = st.text_input("Substances (comma-separated)", 
                                                 value=family_history.get(f'{member.lower()}_substances', ''),
                                                 key=f"family_{member}_substances")
                        family_history[f'{member.lower()}_substances'] = substances
                    
                    suicide_history = st.checkbox(f"Suicide history", 
                                               value=family_history.get(f'{member.lower()}_suicide', False),
                                               key=f"family_{member}_suicide")
                    family_history[f'{member.lower()}_suicide'] = suicide_history
            
            # Store family history in patient data
            st.session_state.current_patient['family_history'] = family_history
        
        # Medical history
        with st.expander("🏥 Medical History", expanded=True):
            medical_hist = st.session_state.current_patient['medical_history']
            
            st.write("Medical Conditions:")
            cols = st.columns(3)
            condition_count = 0
            for i, (condition, info) in enumerate(MEDICAL_CONDITIONS.items()):
                col = cols[i % 3]
                with col:
                    current_value = medical_hist['conditions'].get(condition, False)
                    new_value = st.checkbox(f"{condition} ({info['code']})", value=current_value, key=f"medical_{condition}")
                    medical_hist['conditions'][condition] = new_value
                    if new_value:
                        condition_count += 1
            
            st.metric("Active Medical Conditions", condition_count)
            
            if condition_count > 3:
                st.warning("⚠️ Multiple comorbidities - Consider medication interactions")
            
            medical_hist['comments'] = st.text_area("Additional medical history", value=medical_hist['comments'])
            
            # Medications for medical conditions
            # Dates of diagnosis
            # Severity/control status
    
    with tab5:
        st.subheader("💊 Medications")
        
        med_hist = st.session_state.current_patient['medication_history']
        
        # Medication adherence assessment
        col1, col2 = st.columns(2)
        with col1:
            med_hist['adherence'] = st.selectbox("Overall Medication Adherence", 
                ["", "Excellent (>95%)", "Good (80-95%)", "Fair (65-80%)", "Poor (<65%)"],
                index=0 if not med_hist['adherence'] else
                ["", "Excellent (>95%)", "Good (80-95%)", "Fair (65-80%)", "Poor (<65%)"].index(med_hist['adherence'])
                if med_hist['adherence'] in ["", "Excellent (>95%)", "Good (80-95%)", "Fair (65-80%)", "Poor (<65%)"] else 0
            )
        with col2:
            if med_hist['adherence'] and 'Poor' in med_hist['adherence']:
                st.error("⚠️ Poor adherence - Investigate barriers")
        
        # Current medications
        with st.expander("💊 Current Medications", expanded=True):
            st.write("Add current medications:")
            
            # Drug interaction checking
            # Dosage validation
            # Medication efficacy tracking
            
            # Add new medication
            with st.form("add_current_med"):
                col1, col2, col3 = st.columns(3)
                with col1:
                    med_name = st.text_input("Medication name")
                with col2:
                    med_dosage = st.text_input("Dosage (e.g., 10mg)")
                with col3:
                    med_frequency = st.selectbox("Frequency", 
                        ["Once daily", "Twice daily", "Three times daily", "Four times daily", "As needed", "Other"])
                
                col1, col2 = st.columns(2)
                with col1:
                    med_indication = st.text_input("Indication")
                with col2:
                    med_prescriber = st.text_input("Prescriber")
                
                submit_button = st.form_submit_button("Add Medication")
                
                if submit_button and med_name:
                    # Validate dosage format
                    dosage_errors = DataValidator.validate_medication_dosage(med_dosage)
                    if dosage_errors:
                        for error in dosage_errors:
                            st.error(error)
                    else:
                        new_med = {
                            'id': str(uuid.uuid4()),
                            'name': med_name,
                            'dosage': med_dosage,
                            'frequency': med_frequency,
                            'indication': med_indication,
                            'prescriber': med_prescriber,
                            'start_date': date.today().isoformat()
                        }
                        med_hist['current_medications'].append(new_med)
                        st.success("Medication added!")
                        logger.info(f"Added medication: {med_name}")
            
            # Display current medications
            if med_hist['current_medications']:
                st.write("Current medications:")
                for i, med in enumerate(med_hist['current_medications']):
                    with st.expander(f"💊 {med['name']} - {med['dosage']}"):
                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            st.write(f"**Dosage:** {med['dosage']}")
                        with col2:
                            st.write(f"**Frequency:** {med['frequency']}")
                        with col3:
                            st.write(f"**Indication:** {med.get('indication', 'Not specified')}")
                        with col4:
                            if st.button("❌ Remove", key=f"remove_current_{i}"):
                                med_hist['current_medications'].pop(i)
                                st.rerun()
                        
                        if med.get('prescriber'):
                            st.write(f"**Prescriber:** {med['prescriber']}")
                        if med.get('start_date'):
                            st.write(f"**Started:** {med['start_date']}")
                
                # Medication summary
                st.metric("Total Current Medications", len(med_hist['current_medications']))
                
                # Check for polypharmacy
                if len(med_hist['current_medications']) > 5:
                    st.warning("⚠️ Polypharmacy detected - Review for interactions and necessity")
        
        # Previous medications
        with st.expander("📜 Previous Medications", expanded=False):
            st.write("Add previous medications:")
            
            # Add new previous medication
            with st.form("add_previous_med"):
                col1, col2, col3 = st.columns(3)
                with col1:
                    med_name = st.text_input("Medication name", key="prev_med_name")
                with col2:
                    med_dosage = st.text_input("Dosage", key="prev_med_dosage")
                with col3:
                    med_duration = st.text_input("Duration used", key="prev_med_duration")
                
                col1, col2 = st.columns(2)
                with col1:
                    med_reason = st.selectbox("Reason for discontinuation", 
                        ["Ineffective", "Side effects", "Completed course", "Patient choice", "Other"],
                        key="prev_med_reason")
                with col2:
                    med_response = st.selectbox("Treatment response",
                        ["Excellent", "Good", "Fair", "Poor", "Unknown"],
                        key="prev_med_response")
                
                submit_button = st.form_submit_button("Add Previous Medication")
                
                if submit_button and med_name:
                    new_med = {
                        'id': str(uuid.uuid4()),
                        'name': med_name,
                        'dosage': med_dosage,
                        'duration': med_duration,
                        'reason': med_reason,
                        'response': med_response,
                        'end_date': date.today().isoformat()
                    }
                    med_hist['previous_medications'].append(new_med)
                    st.success("Previous medication added!")
                    logger.info(f"Added previous medication: {med_name}")
            
            # Display previous medications
            if med_hist['previous_medications']:
                st.write("Previous medications:")
                for i, med in enumerate(med_hist['previous_medications']):
                    with st.expander(f"📜 {med['name']} - {med.get('response', 'Unknown response')}"):
                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            st.write(f"**Dosage:** {med['dosage']}")
                        with col2:
                            st.write(f"**Duration:** {med['duration']}")
                        with col3:
                            st.write(f"**Reason stopped:** {med['reason']}")
                        with col4:
                            if st.button("❌ Remove", key=f"remove_previous_{i}"):
                                med_hist['previous_medications'].pop(i)
                                st.rerun()
                        
                        st.write(f"**Treatment response:** {med.get('response', 'Unknown')}")
        
        # Allergies and adverse reactions
        with st.expander("🚨 Allergies & Adverse Reactions", expanded=True):
            med_hist['allergies'] = st.text_area("Drug allergies and adverse reactions", 
                                               value=med_hist['allergies'],
                                               placeholder="List any known drug allergies or adverse reactions...")
            
            if med_hist['allergies']:
                st.warning("⚠️ Allergies documented - Check before prescribing")
    
    with tab6:
        st.subheader("🔬 Mental Status Examination & Laboratory Results")
        
        # MSE
        with st.expander("🧠 Mental Status Examination", expanded=True):
            mse = st.session_state.current_patient['mse']
            
            # MSE scoring system
            # Structured interview guide
            
            col1, col2 = st.columns(2)
            with col1:
                st.subheader("Appearance & Behavior")
                mse['appearance'] = st.selectbox("Appearance", [""] + MSE_OPTIONS['appearance'], 
                    index=0 if not mse['appearance'] else MSE_OPTIONS['appearance'].index(mse['appearance']) + 1
                    if mse['appearance'] in MSE_OPTIONS['appearance'] else 0)
                mse['behavior'] = st.selectbox("Behavior", [""] + MSE_OPTIONS['behavior'],
                    index=0 if not mse['behavior'] else MSE_OPTIONS['behavior'].index(mse['behavior']) + 1
                    if mse['behavior'] in MSE_OPTIONS['behavior'] else 0)
                mse['speech'] = st.selectbox("Speech", [""] + MSE_OPTIONS['speech'],
                    index=0 if not mse['speech'] else MSE_OPTIONS['speech'].index(mse['speech']) + 1
                    if mse['speech'] in MSE_OPTIONS['speech'] else 0)
                
                st.subheader("Mood & Affect")
                mse['mood'] = st.selectbox("Mood", [""] + MSE_OPTIONS['mood'],
                    index=0 if not mse['mood'] else MSE_OPTIONS['mood'].index(mse['mood']) + 1
                    if mse['mood'] in MSE_OPTIONS['mood'] else 0)
                mse['affect'] = st.selectbox("Affect", [""] + MSE_OPTIONS['affect'],
                    index=0 if not mse['affect'] else MSE_OPTIONS['affect'].index(mse['affect']) + 1
                    if mse['affect'] in MSE_OPTIONS['affect'] else 0)
            
            with col2:
                st.subheader("Thought & Perception")
                mse['thought_process'] = st.selectbox("Thought Process", [""] + MSE_OPTIONS['thought_process'],
                    index=0 if not mse['thought_process'] else MSE_OPTIONS['thought_process'].index(mse['thought_process']) + 1
                    if mse['thought_process'] in MSE_OPTIONS['thought_process'] else 0)
                mse['thought_content'] = st.selectbox("Thought Content", [""] + MSE_OPTIONS['thought_content'],
                    index=0 if not mse['thought_content'] else MSE_OPTIONS['thought_content'].index(mse['thought_content']) + 1
                    if mse['thought_content'] in MSE_OPTIONS['thought_content'] else 0)
                mse['perception'] = st.selectbox("Perception", [""] + MSE_OPTIONS['perception'],
                    index=0 if not mse['perception'] else MSE_OPTIONS['perception'].index(mse['perception']) + 1
                    if mse['perception'] in MSE_OPTIONS['perception'] else 0)
                
                st.subheader("Cognition & Insight")
                mse['cognition'] = st.selectbox("Cognition", [""] + MSE_OPTIONS['cognition'],
                    index=0 if not mse['cognition'] else MSE_OPTIONS['cognition'].index(mse['cognition']) + 1
                    if mse['cognition'] in MSE_OPTIONS['cognition'] else 0)
                mse['insight'] = st.selectbox("Insight", [""] + MSE_OPTIONS['insight'],
                    index=0 if not mse['insight'] else MSE_OPTIONS['insight'].index(mse['insight']) + 1
                    if mse['insight'] in MSE_OPTIONS['insight'] else 0)
                mse['judgment'] = st.selectbox("Judgment", [""] + MSE_OPTIONS['judgment'],
                    index=0 if not mse['judgment'] else MSE_OPTIONS['judgment'].index(mse['judgment']) + 1
                    if mse['judgment'] in MSE_OPTIONS['judgment'] else 0)
                mse['orientation'] = st.selectbox("Orientation", [""] + MSE_OPTIONS['orientation'],
                    index=0 if not mse['orientation'] else MSE_OPTIONS['orientation'].index(mse['orientation']) + 1
                    if mse['orientation'] in MSE_OPTIONS['orientation'] else 0)
            
            # MSE completion indicator
            mse_fields_completed = sum(1 for field in mse.values() if field)
            mse_completion = mse_fields_completed / len(mse) * 100
            st.progress(mse_completion / 100)
            st.write(f"MSE Completion: {mse_completion:.0f}% ({mse_fields_completed}/{len(mse)} fields)")
        
        # Laboratory results
        with st.expander("🔬 Laboratory Results", expanded=True):
            labs = st.session_state.current_patient['paraclinicals']
            
            # Reference ranges and flagging
            # Trend analysis for serial labs
            # Lab interpretation suggestions
            
            for category, tests in LAB_TESTS.items():
                with st.expander(f"{category.upper()} - {len([t for t in tests if labs[category].get(t)])} of {len(tests)} completed"):
                    cols = st.columns(3)
                    for i, test in enumerate(tests):
                        col = cols[i % 3]
                        with col:
                            value = st.text_input(f"{test}", value=labs[category].get(test, ''), 
                                                key=f"lab_{category}_{test}")
                            labs[category][test] = value
                            
                            # Show reference range
                            if 'range' in tests[test]:
                                min_val, max_val = tests[test]['range']
                                unit = tests[test].get('unit', '')
                                st.caption(f"Range: {min_val}-{max_val} {unit}")
            
            # Lab value validation
            lab_warnings = validate_lab_values(labs)
            if lab_warnings:
                st.warning("⚠️ Abnormal Lab Values Detected:")
                for warning in lab_warnings:
                    st.warning(f"• {warning}")
            
            # Lab completion summary
            total_tests = sum(len(tests) for tests in LAB_TESTS.values())
            completed_tests = sum(1 for category_labs in labs.values() 
                                for test_value in category_labs.values() if test_value)
            lab_completion = completed_tests / total_tests * 100
            
            st.metric("Lab Completion", f"{lab_completion:.0f}%")
            st.progress(lab_completion / 100)
        
        # Diagnosis
        with st.expander("🏥 Diagnosis", expanded=True):
            diagnosis = st.session_state.current_patient['diagnosis']
            
            # ICD-10/DSM-5 code validation
            # Diagnosis suggestion based on symptoms
            # Differential diagnosis tracking
            
            # Add new diagnosis
            with st.form("add_diagnosis"):
                col1, col2 = st.columns(2)
                with col1:
                    dx_code = st.text_input("Diagnosis code (ICD-10/DSM-5)")
                with col2:
                    dx_description = st.text_input("Diagnosis description")
                
                col1, col2 = st.columns(2)
                with col1:
                    dx_type = st.selectbox("Diagnosis type", ["Primary", "Secondary", "Provisional", "Rule out"])
                with col2:
                    dx_confidence = st.selectbox("Confidence level", ["High", "Moderate", "Low"])
                
                submit_button = st.form_submit_button("Add Diagnosis")
                
                if submit_button and dx_code and dx_description:
                    new_dx = {
                        'id': str(uuid.uuid4()),
                        'code': dx_code,
                        'description': dx_description,
                        'type': dx_type,
                        'confidence': dx_confidence,
                        'date': date.today().isoformat()
                    }
                    diagnosis.append(new_dx)
                    st.success("Diagnosis added!")
                    logger.info(f"Added diagnosis: {dx_code} - {dx_description}")
            
            # Display diagnoses
            if diagnosis:
                st.write("Current diagnoses:")
                primary_count = sum(1 for dx in diagnosis if dx.get('type') == 'Primary')
                
                for i, dx in enumerate(diagnosis):
                    with st.expander(f"{dx.get('type', 'Unknown')} - {dx['code']}: {dx['description']}"):
                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            st.write(f"**Code:** {dx['code']}")
                        with col2:
                            st.write(f"**Type:** {dx.get('type', 'Unknown')}")
                        with col3:
                            st.write(f"**Confidence:** {dx.get('confidence', 'Unknown')}")
                        with col4:
                            if st.button("❌ Remove", key=f"remove_dx_{i}"):
                                diagnosis.pop(i)
                                st.rerun()
                        
                        if dx.get('date'):
                            st.write(f"**Date added:** {dx['date']}")
                
                # Diagnosis validation
                if primary_count == 0:
                    st.warning("⚠️ No primary diagnosis specified")
                elif primary_count > 1:
                    st.warning(f"⚠️ Multiple primary diagnoses ({primary_count}) - Consider reviewing")
            else:
                st.info("No diagnoses recorded")
        
        # Treatment plan section
        with st.expander("📋 Treatment Plan", expanded=False):
            treatment_plan = st.session_state.current_patient.get('treatment_plan', {})
            
            st.subheader("Medications")
            treatment_plan['medications'] = st.text_area("Medication plan", 
                                                       value=treatment_plan.get('medications', ''),
                                                       placeholder="List planned medications, dosages, and rationale...")
            
            st.subheader("Therapy")
            therapy_types = ["CBT", "DBT", "Psychodynamic", "Group therapy", "Family therapy", "Other"]
            for therapy in therapy_types:
                treatment_plan[f'therapy_{therapy.lower().replace(" ", "_")}'] = st.checkbox(therapy, 
                    value=treatment_plan.get(f'therapy_{therapy.lower().replace(" ", "_")}', False),
                    key=f"therapy_{therapy}")
            
            st.subheader("Other Interventions")
            treatment_plan['other_interventions'] = st.text_area("Other interventions", 
                                                               value=treatment_plan.get('other_interventions', ''),
                                                               placeholder="Other planned interventions...")
            
            st.subheader("Follow-up")
            treatment_plan['follow_up'] = st.date_input("Next follow-up", 
                                                     value=datetime.strptime(treatment_plan.get('follow_up', date.today().isoformat()), '%Y-%m-%d').date() 
                                                     if treatment_plan.get('follow_up') else date.today())
            
            # Store treatment plan
            st.session_state.current_patient['treatment_plan'] = treatment_plan
        
        # Progress notes section
        with st.expander("📝 Progress Notes", expanded=False):
            progress_notes = st.session_state.current_patient.get('progress_notes', [])
            
            # Add new note
            with st.form("add_progress_note"):
                note_type = st.selectbox("Note type", ["Initial assessment", "Follow-up", "Medication review", "Therapy session", "Other"])
                note_content = st.text_area("Note content", height=150)
                
                submit_button = st.form_submit_button("Add Note")
                
                if submit_button and note_content:
                    new_note = {
                        'id': str(uuid.uuid4()),
                        'type': note_type,
                        'content': note_content,
                        'date': datetime.now().isoformat(),
                        'author': 'Clinician'  # Could be user authentication
                    }
                    progress_notes.append(new_note)
                    st.success("Progress note added!")
                    logger.info("Progress note added")
            
            # Display notes
            if progress_notes:
                st.write("Progress notes:")
                for i, note in enumerate(reversed(progress_notes)):  # Show newest first
                    with st.expander(f"{note['type']} - {note['date'][:10]}"):
                        st.write(note['content'])
                        if st.button("❌ Remove", key=f"remove_note_{i}"):
                            progress_notes.pop(len(progress_notes) - 1 - i)
                            st.rerun()
            
            # Store progress notes
            st.session_state.current_patient['progress_notes'] = progress_notes
        
        # Outcome measures tracking
        with st.expander("📊 Outcome Measures", expanded=False):
            outcome_measures = st.session_state.current_patient.get('outcome_measures', {})
            
            st.write("Track treatment outcomes over time")
            
            # CGI (Clinical Global Impression)
            st.subheader("Clinical Global Impression (CGI)")
            cgi_severity = st.selectbox("Severity of illness", 
                                       ["Normal", "Borderline mentally ill", "Mildly ill", 
                                        "Moderately ill", "Markedly ill", "Severely ill", "Among the most extremely ill"],
                                       index=0 if not outcome_measures.get('cgi_severity') else 
                                       ["Normal", "Borderline mentally ill", "Mildly ill", 
                                        "Moderately ill", "Markedly ill", "Severely ill", "Among the most extremely ill"]
                                       .index(outcome_measures.get('cgi_severity', 'Normal')),
                                       key="cgi_severity")
            outcome_measures['cgi_severity'] = cgi_severity
            
            cgi_improvement = st.selectbox("Global improvement", 
                                          ["Very much improved", "Much improved", "Minimally improved", 
                                           "No change", "Minimally worse", "Much worse", "Very much worse"],
                                          index=0 if not outcome_measures.get('cgi_improvement') else 
                                          ["Very much improved", "Much improved", "Minimally improved", 
                                           "No change", "Minimally worse", "Much worse", "Very much worse"]
                                          .index(outcome_measures.get('cgi_improvement', 'Very much improved')),
                                          key="cgi_improvement")
            outcome_measures['cgi_improvement'] = cgi_improvement
            
            # GAF (Global Assessment of Functioning)
            st.subheader("Global Assessment of Functioning (GAF)")
            gaf_score = st.slider("GAF Score (0-100)", 0, 100, 
                                  int(outcome_measures.get('gaf_score', 50)),
                                  key="gaf_score")
            outcome_measures['gaf_score'] = gaf_score
            
            # GAF interpretation
            if gaf_score >= 91:
                st.success("Superior functioning")
            elif gaf_score >= 81:
                st.success("Good functioning")
            elif gaf_score >= 71:
                st.info("Mild symptoms")
            elif gaf_score >= 61:
                st.info("Moderate symptoms")
            elif gaf_score >= 51:
                st.warning("Serious symptoms")
            elif gaf_score >= 41:
                st.warning("Major impairment")
            elif gaf_score >= 31:
                st.error("Unable to function")
            else:
                st.error("Persistent danger")
            
            # SOFAS (Social and Occupational Functioning Assessment Scale)
            st.subheader("SOFAS")
            sofas_social = st.slider("Social functioning", 0, 10, 
                                      int(outcome_measures.get('sofas_social', 5)),
                                      key="sofas_social")
            sofas_occupational = st.slider("Occupational functioning", 0, 10, 
                                          int(outcome_measures.get('sofas_occupational', 5)),
                                          key="sofas_occupational")
            
            outcome_measures['sofas_social'] = sofas_social
            outcome_measures['sofas_occupational'] = sofas_occupational
            
            # Store outcome measures
            st.session_state.current_patient['outcome_measures'] = outcome_measures
    
    # Display validation errors and warnings
    if st.session_state.validation_errors:
        with st.container():
            st.error("❌ Validation Errors:")
            for error in st.session_state.validation_errors:
                st.error(f"• {error}")
    
    if st.session_state.validation_warnings:
        with st.container():
            st.warning("⚠️ Validation Warnings:")
            for warning in st.session_state.validation_warnings:
                st.warning(f"• {warning}")
    
    # Autosave functionality
    try:
        autosave_patient()
    except Exception as e:
        logger.error(f"Autosave failed: {e}")
    
    # Footer with system information
    with st.container():
        st.markdown("---")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.caption(f"Session: {st.session_state.session_id[:8]}...")
        with col2:
            if st.session_state.current_patient.get('last_modified'):
                last_mod = datetime.fromisoformat(st.session_state.current_patient['last_modified'])
                st.caption(f"Last modified: {last_mod.strftime('%Y-%m-%d %H:%M')}")
        with col3:
            st.caption(f"Database: {len(st.session_state.patients)} patients")
    
    # Custom CSS for better styling
    st.markdown("""
    <style>
        .stExpander > div:first-child {
            background-color: #f0f2f6;
        }
        .stSelectbox > div > div {
            background-color: white;
        }
        .stTextInput > div > div {
            background-color: white;
        }
        .stTextArea > div > div {
            background-color: white;
        }
        .stDateInput > div > div {
            background-color: white;
        }
        /* Custom styling for risk alerts */
        .risk-alert {
            border-left: 5px solid #ff4444;
            padding: 10px;
            margin: 10px 0;
            background-color: #ffeeee;
        }
        /* Progress indicators */
        .progress-good {
            color: #28a745;
        }
        .progress-warning {
            color: #ffc107;
        }
        .progress-danger {
            color: #dc3545;
        }
    </style>
    """, unsafe_allow_html=True)

# Additional utility functions
def backup_database():
    """Create a backup of the database"""
    try:
        conn = sqlite3.connect(db_manager.db_path)
        backup_path = f"psychiatric_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        
        # Create backup
        backup_conn = sqlite3.connect(backup_path)
        conn.backup(backup_conn)
        backup_conn.close()
        conn.close()
        
        logger.info(f"Database backup created: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Backup failed: {e}")
        return None

def restore_database(backup_file):
    """Restore database from backup"""
    try:
        # Close existing connections
        conn = sqlite3.connect(db_manager.db_path)
        conn.close()
        
        # Replace with backup
        backup_conn = sqlite3.connect(backup_file)
        restore_conn = sqlite3.connect(db_manager.db_path)
        backup_conn.backup(restore_conn)
        restore_conn.close()
        backup_conn.close()
        
        logger.info(f"Database restored from: {backup_file}")
        return True
    except Exception as e:
        logger.error(f"Restore failed: {e}")
        return False

def generate_report(patient_code: str) -> str:
    """Generate a comprehensive assessment report"""
    patient_data = db_manager.load_patient(patient_code)
    if not patient_data:
        return "Patient not found"
    
    report = f"""
# Psychiatric Assessment Report
**Patient Code:** {patient_code}
**Date:** {datetime.now().strftime('%Y-%m-%d')}

## Demographics
- **Age:** {patient_data['demographics'].get('age', 'Not specified')}
- **Gender:** {patient_data['demographics'].get('gender', 'Not specified')}
- **Occupation:** {patient_data['demographics'].get('occupation', 'Not specified')}

## Present Illness
"""
    
    # Add symptoms
    for category, symptoms in patient_data['present_illness'].items():
        present_symptoms = [symptom for symptom, present in symptoms.items() if present]
        if present_symptoms:
            report += f"\n### {category.title()}\n"
            for symptom in present_symptoms:
                report += f"- {symptom}\n"
    
    # Add risk assessment
    risk_assessment = patient_data['risk_assessment']
    report += "\n## Risk Assessment\n"
    
    if risk_assessment['suicidal']['present']:
        report += f"- **Suicidal ideation:** Present (Severity: {risk_assessment['suicidal']['severity']})\n"
        report += f"- **Details:** {risk_assessment['suicidal']['details']}\n"
    
    if risk_assessment['homicidal']['present']:
        report += f"- **Homicidal ideation:** Present (Severity: {risk_assessment['homicidal']['severity']})\n"
        report += f"- **Details:** {risk_assessment['homicidal']['details']}\n"
    
    # Add diagnosis
    if patient_data['diagnosis']:
        report += "\n## Diagnosis\n"
        for dx in patient_data['diagnosis']:
            report += f"- **{dx['type']}:** {dx['code']} - {dx['description']}\n"
    
    # Add treatment plan
    if 'treatment_plan' in patient_data:
        report += "\n## Treatment Plan\n"
        tp = patient_data['treatment_plan']
        if tp.get('medications'):
            report += f"**Medications:** {tp['medications']}\n"
        
        therapies = [therapy.replace('therapy_', '').replace('_', ' ').title() 
                   for therapy, selected in tp.items() 
                   if therapy.startswith('therapy_') and selected]
        if therapies:
            report += f"**Therapy:** {', '.join(therapies)}\n"
        
        if tp.get('other_interventions'):
            report += f"**Other interventions:** {tp['other_interventions']}\n"
        
        if tp.get('follow_up'):
            report += f"**Follow-up:** {tp['follow_up']}\n"
    
    # Add outcome measures
    if 'outcome_measures' in patient_data:
        report += "\n## Outcome Measures\n"
        om = patient_data['outcome_measures']
        if om.get('cgi_severity'):
            report += f"**CGI Severity:** {om['cgi_severity']}\n"
        if om.get('cgi_improvement'):
            report += f"**CGI Improvement:** {om['cgi_improvement']}\n"
        if om.get('gaf_score'):
            report += f"**GAF Score:** {om['gaf_score']}\n"
        if om.get('sofas_social') is not None:
            report += f"**SOFAS Social:** {om['sofas_social']}\n"
        if om.get('sofas_occupational') is not None:
            report += f"**SOFAS Occupational:** {om['sofas_occupational']}\n"
    
    return report

def export_for_research(anonymize: bool = True):
    """Export data for research purposes"""
    patients_data = []
    for patient_info in st.session_state.patients:
        patient_data = db_manager.load_patient(patient_info['patient_code'])
        if patient_data:
            if anonymize:
                # Remove identifying information
                patient_data['patient_code'] = f"PATIENT_{uuid.uuid4().hex[:8]}"
                patient_data['demographics']['occupation'] = ""
                # Remove any other identifying fields
                
            patients_data.append(patient_data)
    
    if patients_data:
        data = json.dumps(patients_data, indent=2)
        st.download_button(
            label="Download Research Data",
            data=data,
            file_name=f"psychiatric_research_data_{datetime.now().strftime('%Y%m%d')}.json",
            mime="application/json"
        )
        logger.info(f"Exported {len(patients_data)} patients for research")
    else:
        st.warning("No patient data available for export")

# Data quality monitoring
def assess_data_quality() -> Dict[str, float]:
    """Assess overall data quality metrics"""
    quality_metrics = {}
    
    # Completeness
    total_patients = len(st.session_state.patients)
    if total_patients > 0:
        completeness_scores = []
        for patient_info in st.session_state.patients:
            patient_data = db_manager.load_patient(patient_info['patient_code'])
            if patient_data:
                completeness = DataValidator.calculate_data_completeness(patient_data)
                completeness_scores.append(completeness)
        
        quality_metrics['average_completeness'] = sum(completeness_scores) / len(completeness_scores)
        quality_metrics['patients_with_high_completeness'] = sum(1 for score in completeness_scores if score >= 0.8) / total_patients
    else:
        quality_metrics['average_completeness'] = 0
        quality_metrics['patients_with_high_completeness'] = 0
    
    # Consistency (example: check for inconsistent data)
    consistency_issues = 0
    for patient_info in st.session_state.patients:
        patient_data = db_manager.load_patient(patient_info['patient_code'])
        if patient_data:
            # Check for inconsistent risk assessment
            risk_assessment = patient_data.get('risk_assessment', {})
            if risk_assessment.get('suicidal', {}).get('present'):
                if not risk_assessment.get('suicidal', {}).get('severity'):
                    consistency_issues += 1
    
    quality_metrics['consistency_score'] = 1 - (consistency_issues / total_patients) if total_patients > 0 else 1
    
    # Timeliness (how recently data was updated)
    if total_patients > 0:
        recent_updates = 0
        for patient_info in st.session_state.patients:
            if patient_info.get('updated_at'):
                try:
                    update_date = datetime.fromisoformat(patient_info['updated_at'])
                    if (datetime.now() - update_date).days <= 30:
                        recent_updates += 1
                except:
                    pass
        
        quality_metrics['timeliness_score'] = recent_updates / total_patients
    else:
        quality_metrics['timeliness_score'] = 0
    
    return quality_metrics

# Error handling wrapper
def safe_execute(func, *args, **kwargs):
    """Safely execute functions with error handling"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        logger.error(f"Function {func.__name__} failed: {e}")
        st.error(f"An error occurred: {str(e)}")
        return None

if __name__ == "__main__":
    logger.info("Starting psychiatric assessment application")
    try:
        main()
    except Exception as e:
        logger.critical(f"Application failed to start: {e}")
        st.error("Application failed to start. Please check the logs and try again.")
        st.stop()